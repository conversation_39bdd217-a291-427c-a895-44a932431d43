"""
LINE 通知服務
提供 LINE 訊息推送功能
"""

import os
import json
import requests
from typing import Dict, Any, Optional
from datetime import datetime

from src.infrastructure.logging.logger_manager import LoggerManager


class LineNotificationService:
    """
    LINE 通知服務類
    負責發送 LINE 通知訊息
    """
    
    def __init__(self):
        """
        初始化 LINE 通知服務
        """
        self.logger = LoggerManager().get_logger("LineNotificationService")
        self.channel_access_token = os.getenv('LINE_CHANNEL_ACCESS_TOKEN')
        self.user_id = os.getenv('LINE_USER_ID')
        self.base_url = 'https://api.line.me/v2/bot/message/push'
        
        # 通知控制設定
        self.notify_parsing_failure = os.getenv('LINE_NOTIFY_PARSING_FAILURE', 'true').lower() == 'true'
        self.notify_parsing_success = os.getenv('LINE_NOTIFY_PARSING_SUCCESS', 'false').lower() == 'true'
        
        # 通知記錄檔案路徑
        self.notification_log_file = "line_notifications.json"
        
        # 驗證設定
        self._validate_config()
        
        self.logger.info(f"LINE 通知服務已初始化 - 失敗通知: {self.notify_parsing_failure}, 成功通知: {self.notify_parsing_success}")
    
    def _validate_config(self):
        """
        驗證 LINE 設定
        """
        if not self.channel_access_token:
            raise ValueError("LINE_CHANNEL_ACCESS_TOKEN 環境變數未設定")
        
        if not self.user_id:
            raise ValueError("LINE_USER_ID 環境變數未設定")
        
        self.logger.info("LINE 設定驗證通過")
    
    def send_parsing_failure_notification(self, email_data: Dict[str, Any]) -> bool:
        """
        發送解析失敗通知
        
        Args:
            email_data: 郵件資料
            
        Returns:
            bool: 是否成功發送
        """
        try:
            # 檢查是否啟用解析失敗通知
            if not self.notify_parsing_failure:
                self.logger.info("解析失敗通知已停用，跳過通知")
                return True
            
            email_id = email_data.get('id')
            
            # 檢查是否已經通知過
            if self._is_already_notified(email_id, 'parsing_failure'):
                self.logger.info(f"郵件 {email_id} 解析失敗已通知過，跳過通知")
                return True
            
            # 構建通知訊息
            message = self._build_parsing_failure_message(email_data)
            
            # 發送通知
            success = self._send_message(message)
            
            if success:
                # 記錄通知
                self._record_notification(email_id, 'parsing_failure', email_data)
                self.logger.info(f"郵件 {email_id} 解析失敗通知已發送")
            else:
                self.logger.error(f"郵件 {email_id} 解析失敗通知發送失敗")
            
            return success
            
        except Exception as e:
            self.logger.error(f"發送解析失敗通知時發生錯誤: {e}")
            return False
    
    def send_parsing_success_notification(self, email_data: Dict[str, Any]) -> bool:
        """
        發送解析成功通知
        
        Args:
            email_data: 郵件資料
            
        Returns:
            bool: 是否成功發送
        """
        try:
            # 檢查是否啟用解析成功通知
            if not self.notify_parsing_success:
                self.logger.info("解析成功通知已停用，跳過通知")
                return True
            
            email_id = email_data.get('id')
            
            # 檢查是否已經通知過
            if self._is_already_notified(email_id, 'parsing_success'):
                self.logger.info(f"郵件 {email_id} 解析成功已通知過，跳過通知")
                return True
            
            # 構建通知訊息
            message = self._build_parsing_success_message(email_data)
            
            # 發送通知
            success = self._send_message(message)
            
            if success:
                # 記錄通知
                self._record_notification(email_id, 'parsing_success', email_data)
                self.logger.info(f"郵件 {email_id} 解析成功通知已發送")
            else:
                self.logger.error(f"郵件 {email_id} 解析成功通知發送失敗")
            
            return success
            
        except Exception as e:
            self.logger.error(f"發送解析成功通知時發生錯誤: {e}")
            return False
    
    def _build_parsing_failure_message(self, email_data: Dict[str, Any]) -> str:
        """
        構建解析失敗通知訊息
        
        Args:
            email_data: 郵件資料
            
        Returns:
            str: 通知訊息
        """
        email_id = email_data.get('id', 'N/A')
        subject = email_data.get('subject', 'N/A')
        sender = email_data.get('sender', 'N/A')
        received_time = email_data.get('received_time', 'N/A')
        error_message = email_data.get('error_message', 'N/A')
        vendor_code = email_data.get('vendor_code', 'N/A')
        
        # 解析接收時間
        try:
            if received_time != 'N/A':
                received_dt = datetime.fromisoformat(received_time.replace('Z', '+00:00'))
                received_time_str = received_dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                received_time_str = 'N/A'
        except:
            received_time_str = str(received_time)
        
        # 簡化錯誤訊息
        simplified_error = self._simplify_error_message(error_message)
        
        message = f"""🚨 郵件解析失敗通知

📧 郵件資訊:
• ID: {email_id}
• 主旨: {subject}
• 寄件者: {sender}
• 接收時間: {received_time_str}
• 廠商代碼: {vendor_code}

❌ 失敗原因:
{simplified_error}

🔧 建議處理:
• 檢查郵件格式是否正確
• 確認廠商代碼是否支援
• 考慮手動處理或聯絡系統管理員

⏰ 通知時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        return message
    
    def _build_parsing_success_message(self, email_data: Dict[str, Any]) -> str:
        """
        構建解析成功通知訊息
        
        Args:
            email_data: 郵件資料
            
        Returns:
            str: 通知訊息
        """
        email_id = email_data.get('id', 'N/A')
        subject = email_data.get('subject', 'N/A')
        sender = email_data.get('sender', 'N/A')
        received_time = email_data.get('received_time', 'N/A')
        vendor_code = email_data.get('vendor_code', 'N/A')
        extraction_method = email_data.get('extraction_method', 'N/A')
        pd = email_data.get('pd', 'N/A')
        lot = email_data.get('lot', 'N/A')
        yield_value = email_data.get('yield_value', 'N/A')
        
        # 解析接收時間
        try:
            if received_time != 'N/A':
                received_dt = datetime.fromisoformat(received_time.replace('Z', '+00:00'))
                received_time_str = received_dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                received_time_str = 'N/A'
        except:
            received_time_str = str(received_time)
        
        # 解析方法顯示名稱
        method_display = {
            'traditional': '傳統解析',
            'llm': 'LLM解析',
            'hybrid': '混合解析'
        }.get(extraction_method, extraction_method)
        
        message = f"""✅ 郵件解析成功通知

📧 郵件資訊:
• ID: {email_id}
• 主旨: {subject}
• 寄件者: {sender}
• 接收時間: {received_time_str}

📊 解析結果:
• 廠商代碼: {vendor_code}
• 產品代碼: {pd}
• 批次編號: {lot}
• 良率: {yield_value}
• 解析方法: {method_display}

⏰ 通知時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        return message
    
    def _simplify_error_message(self, error_message: str) -> str:
        """
        簡化錯誤訊息
        
        Args:
            error_message: 原始錯誤訊息
            
        Returns:
            str: 簡化後的錯誤訊息
        """
        if not error_message or error_message == 'N/A':
            return "無具體錯誤資訊"
        
        # 根據錯誤訊息類型提供友善的說明
        error_lower = error_message.lower()
        
        if 'jcet' in error_lower and 'validation' in error_lower:
            return "JCET 郵件格式驗證失敗"
        elif 'gtk' in error_lower and 'validation' in error_lower:
            return "GTK 郵件格式驗證失敗"
        elif 'llm' in error_lower:
            return "LLM 分析失敗"
        elif 'timeout' in error_lower:
            return "處理超時"
        elif 'parsing failed' in error_lower:
            return "解析引擎失敗"
        else:
            # 如果錯誤訊息太長，截取前100個字符
            if len(error_message) > 100:
                return error_message[:100] + "..."
            return error_message
    
    def _send_message(self, message: str) -> bool:
        """
        發送 LINE 訊息
        
        Args:
            message: 訊息內容
            
        Returns:
            bool: 是否成功發送
        """
        try:
            headers = {
                'Authorization': f'Bearer {self.channel_access_token}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'to': self.user_id,
                'messages': [
                    {
                        'type': 'text',
                        'text': message
                    }
                ]
            }
            
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                self.logger.info("LINE 訊息發送成功")
                return True
            else:
                self.logger.error(f"LINE 訊息發送失敗: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"LINE 訊息發送網路錯誤: {e}")
            return False
        except Exception as e:
            self.logger.error(f"LINE 訊息發送時發生錯誤: {e}")
            return False
    
    def _is_already_notified(self, email_id: str, notification_type: str) -> bool:
        """
        檢查是否已經通知過
        
        Args:
            email_id: 郵件 ID
            notification_type: 通知類型
            
        Returns:
            bool: 是否已通知
        """
        try:
            if not os.path.exists(self.notification_log_file):
                return False
            
            with open(self.notification_log_file, 'r', encoding='utf-8') as f:
                notifications = json.load(f)
            
            # 檢查是否存在相同的通知記錄
            for notification in notifications:
                if (notification.get('email_id') == str(email_id) and 
                    notification.get('type') == notification_type):
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"檢查通知記錄時發生錯誤: {e}")
            return False
    
    def _record_notification(self, email_id: str, notification_type: str, email_data: Dict[str, Any]):
        """
        記錄通知
        
        Args:
            email_id: 郵件 ID
            notification_type: 通知類型
            email_data: 郵件資料
        """
        try:
            # 載入現有記錄
            notifications = []
            if os.path.exists(self.notification_log_file):
                with open(self.notification_log_file, 'r', encoding='utf-8') as f:
                    notifications = json.load(f)
            
            # 新增通知記錄
            notification_record = {
                'email_id': str(email_id),
                'type': notification_type,
                'timestamp': datetime.now().isoformat(),
                'email_subject': email_data.get('subject', 'N/A'),
                'email_sender': email_data.get('sender', 'N/A'),
                'error_message': email_data.get('error_message', 'N/A')
            }
            
            notifications.append(notification_record)
            
            # 保留最近500筆記錄，避免檔案過大
            if len(notifications) > 500:
                notifications = notifications[-500:]
            
            # 儲存記錄
            with open(self.notification_log_file, 'w', encoding='utf-8') as f:
                json.dump(notifications, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"通知記錄已儲存: {email_id} - {notification_type}")
            
        except Exception as e:
            self.logger.error(f"記錄通知時發生錯誤: {e}")
    
    def get_notification_history(self, limit: int = 50) -> list:
        """
        獲取通知歷史記錄
        
        Args:
            limit: 返回記錄數量限制
            
        Returns:
            list: 通知歷史記錄
        """
        try:
            if not os.path.exists(self.notification_log_file):
                return []
            
            with open(self.notification_log_file, 'r', encoding='utf-8') as f:
                notifications = json.load(f)
            
            # 按時間戳排序並返回指定數量
            notifications.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            return notifications[:limit]
            
        except Exception as e:
            self.logger.error(f"獲取通知歷史記錄時發生錯誤: {e}")
            return []
    
    def clear_notification_history(self) -> bool:
        """
        清除通知歷史記錄
        
        Returns:
            bool: 是否成功清除
        """
        try:
            if os.path.exists(self.notification_log_file):
                os.remove(self.notification_log_file)
                self.logger.info("通知歷史記錄已清除")
                return True
            return True
            
        except Exception as e:
            self.logger.error(f"清除通知歷史記錄時發生錯誤: {e}")
            return False
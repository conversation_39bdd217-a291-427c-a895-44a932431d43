"""
JCET 廠商解析器實作
基於 VBA JCETInfoFromStrings 邏輯

VBA 邏輯參考：
- 識別條件：InStr(LCase(body), "jcet") Or InStr(LCase(senderAddress), "jcetglobal.com")
- 解析規則：尋找包含 KUI/GYC 的字串，長度 > 4 取前 15 字符作為 MO
- 若長度 <= 4，記錄位置，後面的詞作為 lot 和 product
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult


class JCETParser(VendorParser):
    """
    JCET 廠商郵件解析器
    
    識別條件：
    - 郵件正文包含 "jcet"
    - 寄件者地址包含 "jcetglobal.com"
    
    解析機制：
    1. 尋找包含 KUI 或 GYC 的詞
    2. 如果詞長度 > 4：取前 15 字符作為 MO 編號
    3. 如果詞長度 <= 4：該詞後的第一個詞作為 lot，第二個詞作為 product
    """
    
    def __init__(self):
        """初始化 JCET 解析器"""
        super().__init__()
        self._vendor_code = "JCET"
        self._vendor_name = "JCET"
        self._identification_patterns = [
            "jcet",              # 內文關鍵字
            "jcetglobal.com",    # 寄件者域名
            "致新",              # 中文公司名稱
            "chipson",           # 英文公司名稱
            "taiwan"             # 台灣關鍵字
        ]
        self.set_confidence_threshold(0.8)
        
        # JCET 特有的 MO 編號模式
        self.mo_patterns = [
            r'\b\w*KUI\w*\b',      # 包含 'KUI' 的詞
            r'\b\w*GYC\w*\b'       # 包含 'GYC' 的詞
        ]

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject = email_data.subject or ""
        body = email_data.body or ""
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        content = (subject + " " + body).lower()
        
        # 檢查所有識別模式
        for pattern in self._identification_patterns:
            if pattern.lower() in content:
                matched_patterns.append(pattern)
                confidence_score += 0.8  # 每個模式增加信心分數以達到閾值
        
        # VBA: InStr(1, LCase(body), "jcet", vbTextCompare) > 0
        if "jcet" in body.lower():
            confidence_score += 0.4
            
        # 也檢查主旨中的 jcet（增強識別能力）
        if "jcet" in subject.lower():
            confidence_score += 0.3
            
        # VBA: InStr(1, LCase(senderAddress), "jcetglobal.com", vbTextCompare) > 0
        if "jcetglobal.com" in sender_lower:
            matched_patterns.append("jcetglobal.com")
            confidence_score += 0.6
        
        # 額外的信心分數計算
        if "jcet" in sender_lower:
            confidence_score += 0.3  # JCET 相關寄件者
            
        # 檢查主旨是否有 KUI/GYC 模式
        for pattern in self.mo_patterns:
            if re.search(pattern, subject, re.IGNORECASE):
                confidence_score += 0.2
                break
                
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="jcet_keyword_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        if not email_data.subject:
            # 空主旨時嘗試從識別結果判斷
            identification = self.identify_vendor(email_data)
            if not identification.is_identified:
                raise ParsingError("Empty subject and cannot identify vendor", vendor_code=self.vendor_code)
        
        try:
            # 使用 JCET 關鍵字解析機制
            jcet_result = self.parse_jcet_keywords(email_data.subject or "")
            
            # 清理和驗證 MO 編號格式
            mo_number = jcet_result["mo_number"]
            if mo_number and mo_number != "":
                # JCET MO 編號支援多種格式，包括 JCET 特有的格式
                # 檢查標準格式
                mo_match = re.search(r'([A-Z]\d{6})', mo_number)
                if mo_match:
                    mo_number = mo_match.group(1)
                else:
                    # 檢查 JCET 格式 (如 YHW0049.Y)
                    jcet_match = re.search(r'([A-Z]{2,4}\d{4,6}\.[A-Z]{1,2})', mo_number)
                    if jcet_match:
                        mo_number = jcet_match.group(1)
                    else:
                        # 如果是其他格式，保持原樣（讓 email_models.py 進行最終驗證）
                        mo_number = mo_number
            else:
                mo_number = None
            
            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=mo_number,
                lot_number=jcet_result["lot_number"] if jcet_result["lot_number"] != "" else None,
                is_success=True,
                error_message=None,
                extraction_method="traditional",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': jcet_result["product"],
                    'lot_number': jcet_result["lot_number"],
                    'mo_number': jcet_result["mo_number"],  # 原始 MO 編號
                    'parsing_method': jcet_result["method"],
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'jcet_kui_gyc_parsing'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                extraction_method="traditional",
                is_success=False,
                error_message=f"JCET parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_jcet_keywords(self, subject: str) -> Dict[str, Any]:
        """
        解析 JCET 關鍵字：KUI/GYC 模式 + LOT 編號模式
        基於 VBA JCETInfoFromStrings 邏輯並支援 LOT 編號解析
        
        VBA 邏輯：
        - 尋找包含 KUI 或 GYC 的詞
        - 如果詞長度 > 4：取前 15 字符作為 MO 編號
        - 如果詞長度 <= 4：記錄位置，後面的詞作為 lot 和 product
        - 新增：尋找 lot：XXX 或 测试批号：XXX 模式
        """
        if not subject:
            return {
                "mo_number": "",
                "lot_number": "",
                "product": "",
                "method": "no_pattern"
            }
            
        words = subject.split()
        mo_number = ""
        lot_number = ""
        product = ""
        method = "no_pattern"
        first_word_idx = 0
        
        # 第一階段：尋找長模式的 KUI/GYC（長度 > 4）
        for i, word in enumerate(words):
            word_upper = word.upper()
            if len(word) > 4:
                if "KUI" in word_upper:
                    mo_number = word[:15]  # 取前 15 字符
                    method = "kui_pattern_long"
                    return {
                        "mo_number": mo_number,
                        "lot_number": lot_number,
                        "product": product,
                        "method": method
                    }
                elif "GYC" in word_upper:
                    mo_number = word[:15]  # 取前 15 字符
                    method = "gyc_pattern_long"
                    return {
                        "mo_number": mo_number,
                        "lot_number": lot_number,
                        "product": product,
                        "method": method
                    }
        
        # 第二階段：尋找短模式的 KUI/GYC（長度 <= 4）
        for i, word in enumerate(words):
            word_upper = word.upper()
            if len(word) <= 4:
                if (word_upper == "KUI" or word_upper == "GYC") and first_word_idx == 0:
                    first_word_idx = i
                    if word_upper == "KUI":
                        method = "kui_pattern_short"
                    else:
                        method = "gyc_pattern_short"
                    break
        
        # 第三階段：根據關鍵字位置解析 lot 和 product
        if first_word_idx >= 0 and len(words) > first_word_idx and method != "no_pattern":
            for i in range(first_word_idx + 1, len(words)):
                if len(words[i]) > 0:
                    lot_number = words[i]
                    if i + 1 < len(words):
                        product = words[i + 1]
                    break
        
        # 第四階段：新增 LOT 編號解析模式（適用於沒有 KUI/GYC 的郵件）
        if method == "no_pattern":
            # 尋找 "lot：XXX" 或 "lot: XXX" 模式
            lot_match = re.search(r'lot[：:]\s*([A-Z0-9.]+)', subject, re.IGNORECASE)
            if lot_match:
                lot_number = lot_match.group(1)
                mo_number = lot_number  # 將 lot 編號作為 MO 編號
                method = "lot_pattern"
            
            # 尋找 "测试批号：XXX" 模式
            test_batch_match = re.search(r'测试批号[：:]\s*([A-Z0-9.]+)', subject, re.IGNORECASE)
            if test_batch_match:
                test_batch_number = test_batch_match.group(1)
                # 如果沒有找到 lot 編號，使用测试批号
                if not lot_number:
                    lot_number = test_batch_number
                    mo_number = test_batch_number
                    method = "test_batch_pattern"
                
            # 尋找產品型號 (如 G2892K21D)
            product_match = re.search(r'(G\d{4}[A-Z]\d{2}[A-Z])', subject, re.IGNORECASE)
            if product_match:
                product = product_match.group(1)
                if method == "no_pattern":
                    method = "product_pattern"
        
        return {
            "mo_number": mo_number,
            "lot_number": lot_number,
            "product": product,
            "method": method
        }

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_formats': [
                'KUI pattern matching (long: >4 chars)',
                'GYC pattern matching (long: >4 chars)',
                'KUI pattern matching (short: <=4 chars)',
                'GYC pattern matching (short: <=4 chars)'
            ],
            'confidence_threshold': self.get_confidence_threshold(),
            'chinese_support': True,
            'based_on': 'VBA JCETInfoFromStrings',
            'extraction_capabilities': [
                'MO number from KUI/GYC pattern (long mode: first 15 chars)',
                'LOT number from position after KUI/GYC (short mode)',
                'Product name from position after LOT (short mode)',
                'Case-insensitive keyword matching',
                'Email body and sender pattern identification'
            ],
            'special_features': [
                'Dual pattern matching (long/short mode)',
                'Position-based extraction for short patterns',
                'Length-based MO extraction for long patterns',
                'Multiple identification methods (body + sender)'
            ]
        }
/**
 * 郵件操作功能模組
 * 負責同步、搜尋、選擇、批量操作等功能
 */

/**
 * 郵件操作管理類
 */
class EmailOperations {
    constructor(inbox) {
        this.inbox = inbox;
    }
    
    /**
     * 同步郵件
     */
    async syncEmails() {
        if (this.inbox.isLoading) return;
        
        EmailUIUtils.showSyncStatus(this.inbox.elements, '正在同步郵件...');
        
        try {
            const response = await fetch('/api/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                },
                body: JSON.stringify({
                    max_emails: 100
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                EmailUIUtils.showNotification(this.inbox.elements, '郵件同步成功', 'success');
                await this.inbox.listManager.loadEmails();
                await this.updateStatistics();
            } else {
                EmailUIUtils.showNotification(this.inbox.elements, '郵件同步失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('同步郵件失敗:', error);
            EmailUIUtils.showNotification(this.inbox.elements, '郵件同步失敗', 'error');
        } finally {
            EmailUIUtils.hideSyncStatus(this.inbox.elements);
        }
    }
    
    /**
     * 切換自動同步
     */
    async toggleAutoSync() {
        if (!this.inbox.autoSyncEnabled) {
            // 啟動自動同步
            await this.startAutoSync();
        } else {
            // 停止自動同步
            await this.stopAutoSync();
        }
    }
    
    /**
     * 啟動自動同步
     */
    async startAutoSync() {
        try {
            const response = await fetch('/api/sync/auto/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    interval: 60  // 60秒
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.inbox.autoSyncEnabled = true;
                if (this.inbox.elements.autoSyncBtn) {
                    this.inbox.elements.autoSyncBtn.innerHTML = '<span class="btn-icon">⏸️</span><span class="btn-text">停止自動同步</span>';
                    this.inbox.elements.autoSyncBtn.classList.add('active');
                }
                EmailUIUtils.showNotification(
                    this.inbox.elements,
                    result.message,
                    'success'
                );
            } else {
                EmailUIUtils.showNotification(
                    this.inbox.elements,
                    '啟動自動同步失敗: ' + result.message,
                    'error'
                );
            }
        } catch (error) {
            console.error('啟動自動同步失敗:', error);
            EmailUIUtils.showNotification(
                this.inbox.elements,
                '啟動自動同步失敗',
                'error'
            );
        }
    }
    
    /**
     * 停止自動同步
     */
    async stopAutoSync() {
        try {
            const response = await fetch('/api/sync/auto/stop', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.inbox.autoSyncEnabled = false;
                if (this.inbox.elements.autoSyncBtn) {
                    this.inbox.elements.autoSyncBtn.innerHTML = '<span class="btn-icon">⏰</span><span class="btn-text">自動同步</span>';
                    this.inbox.elements.autoSyncBtn.classList.remove('active');
                }
                EmailUIUtils.showNotification(
                    this.inbox.elements,
                    result.message,
                    'success'
                );
            } else {
                EmailUIUtils.showNotification(
                    this.inbox.elements,
                    '停止自動同步失敗: ' + result.message,
                    'error'
                );
            }
        } catch (error) {
            console.error('停止自動同步失敗:', error);
            EmailUIUtils.showNotification(
                this.inbox.elements,
                '停止自動同步失敗',
                'error'
            );
        }
    }
    
    /**
     * 切換郵件選擇狀態
     */
    toggleEmailSelection(emailId, selected) {
        if (selected) {
            this.inbox.selectedEmails.add(emailId);
        } else {
            this.inbox.selectedEmails.delete(emailId);
        }
        
        this.updateBatchActions();
        this.inbox.listManager.updateEmailItemSelection(emailId, selected);
    }
    
    /**
     * 全選/取消全選
     */
    toggleSelectAll(selectAll) {
        this.inbox.selectedEmails.clear();
        
        if (selectAll) {
            this.inbox.emails.forEach(email => {
                this.inbox.selectedEmails.add(email.id);
            });
        }
        
        // 更新 UI
        this.inbox.listManager.updateAllEmailItemSelections(selectAll);
        this.updateBatchActions();
    }
    
    /**
     * 更新批量操作面板
     */
    updateBatchActions() {
        const selectedCount = this.inbox.selectedEmails.size;
        
        if (selectedCount > 0) {
            if (this.inbox.elements.batchActions) {
                this.inbox.elements.batchActions.classList.remove('hidden');
            }
            if (this.inbox.elements.selectedCount) {
                this.inbox.elements.selectedCount.textContent = selectedCount;
            }
        } else {
            if (this.inbox.elements.batchActions) {
                this.inbox.elements.batchActions.classList.add('hidden');
            }
        }
    }
    
    /**
     * 批量標記已讀
     */
    async batchMarkRead() {
        if (this.inbox.selectedEmails.size === 0) return;
        
        try {
            const emailIds = Array.from(this.inbox.selectedEmails);
            
            // 模擬 API 調用
            const response = await fetch('/api/emails/batch-mark-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email_ids: emailIds })
            });
            
            const result = await response.json();
            
            if (result.success) {
                EmailUIUtils.showNotification(
                    this.inbox.elements,
                    `已標記 ${this.inbox.selectedEmails.size} 封郵件為已讀`,
                    'success'
                );
                this.inbox.selectedEmails.clear();
                this.updateBatchActions();
                await this.inbox.listManager.refreshEmails();
            } else {
                EmailUIUtils.showNotification(this.inbox.elements, '批量標記失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('批量標記已讀失敗:', error);
            EmailUIUtils.showNotification(this.inbox.elements, '批量標記已讀失敗', 'error');
        }
    }
    
    /**
     * 批量刪除
     */
    async batchDelete() {
        if (this.inbox.selectedEmails.size === 0) return;
        
        const confirmed = await EmailUIUtils.showConfirmDialog(
            this.inbox.elements,
            '確認刪除',
            `您確定要刪除 ${this.inbox.selectedEmails.size} 封郵件嗎？此操作無法撤銷。`
        );
        
        if (confirmed) {
            try {
                const emailIds = Array.from(this.inbox.selectedEmails);
                
                const response = await fetch('/api/emails/batch-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email_ids: emailIds })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    EmailUIUtils.showNotification(
                        this.inbox.elements,
                        `已刪除 ${this.inbox.selectedEmails.size} 封郵件`,
                        'success'
                    );
                    this.inbox.selectedEmails.clear();
                    this.updateBatchActions();
                    await this.inbox.listManager.refreshEmails();
                } else {
                    EmailUIUtils.showNotification(this.inbox.elements, '批量刪除失敗: ' + result.message, 'error');
                }
                
            } catch (error) {
                console.error('批量刪除失敗:', error);
                EmailUIUtils.showNotification(this.inbox.elements, '批量刪除失敗', 'error');
            }
        }
    }
    
    /**
     * 批量處理
     */
    async batchProcess() {
        if (this.inbox.selectedEmails.size === 0) return;
        
        try {
            const emailIds = Array.from(this.inbox.selectedEmails);
            
            const response = await fetch('/api/emails/batch-process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email_ids: emailIds })
            });
            
            const result = await response.json();
            
            if (result.success) {
                EmailUIUtils.showNotification(
                    this.inbox.elements,
                    `已啟動 ${this.inbox.selectedEmails.size} 封郵件的處理流程`,
                    'success'
                );
                this.inbox.selectedEmails.clear();
                this.updateBatchActions();
                await this.inbox.listManager.refreshEmails();
            } else {
                EmailUIUtils.showNotification(this.inbox.elements, '批量處理失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('批量處理失敗:', error);
            EmailUIUtils.showNotification(this.inbox.elements, '批量處理失敗', 'error');
        }
    }
    
    /**
     * 刪除單一郵件
     */
    async deleteEmail(emailId) {
        const confirmed = await EmailUIUtils.showConfirmDialog(
            this.inbox.elements,
            '確認刪除',
            '您確定要刪除這封郵件嗎？此操作無法撤銷。'
        );
        
        if (confirmed) {
            try {
                const response = await fetch(`/api/emails/${emailId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    EmailUIUtils.showNotification(this.inbox.elements, '郵件已刪除', 'success');
                    await this.inbox.listManager.refreshEmails();
                    await this.updateStatistics();
                } else {
                    EmailUIUtils.showNotification(this.inbox.elements, '刪除失敗: ' + result.message, 'error');
                }
                
            } catch (error) {
                console.error('刪除郵件失敗:', error);
                EmailUIUtils.showNotification(this.inbox.elements, '刪除郵件失敗', 'error');
            }
        }
    }
    
    
    /**
     * 處理郵件
     */
    async processEmail(emailId) {
        try {
            const response = await fetch(`/api/emails/${emailId}/process`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                EmailUIUtils.showNotification(this.inbox.elements, '已啟動郵件處理流程', 'success');
                await this.inbox.listManager.refreshEmails();
            } else {
                EmailUIUtils.showNotification(this.inbox.elements, '處理失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('處理郵件失敗:', error);
            EmailUIUtils.showNotification(this.inbox.elements, '處理郵件失敗', 'error');
        }
    }
    
    /**
     * 更新統計資訊
     */
    async updateStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const result = await response.json();
            
            if (result.success) {
                this.inbox.statistics = result.data;
                EmailUIUtils.renderStatistics(this.inbox.elements, this.inbox.statistics);
            }
            
        } catch (error) {
            console.error('更新統計資訊失敗:', error);
        }
    }
    
    /**
     * 標記單一郵件為已讀/未讀
     */
    async toggleEmailReadStatus(emailId, isRead) {
        try {
            const response = await fetch(`/api/emails/${emailId}/read`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ is_read: isRead })
            });
            
            const result = await response.json();
            
            if (result.success) {
                const statusText = isRead ? '已讀' : '未讀';
                EmailUIUtils.showNotification(this.inbox.elements, `郵件已標記為${statusText}`, 'success');
                await this.inbox.listManager.refreshEmails();
                await this.updateStatistics();
            } else {
                EmailUIUtils.showNotification(this.inbox.elements, '狀態更新失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('更新郵件狀態失敗:', error);
            EmailUIUtils.showNotification(this.inbox.elements, '更新郵件狀態失敗', 'error');
        }
    }
    
    /**
     * 清除所有選擇
     */
    clearAllSelections() {
        this.inbox.selectedEmails.clear();
        this.inbox.listManager.updateAllEmailItemSelections(false);
        this.updateBatchActions();
        
        // 更新全選按鈕狀態
        if (this.inbox.elements.selectAllCheckbox) {
            this.inbox.elements.selectAllCheckbox.checked = false;
        }
    }
    
    /**
     * 檢查同步狀態
     */
    async checkSyncStatus() {
        try {
            const response = await fetch('/api/sync/status');
            const result = await response.json();
            
            if (result.success && result.data.is_syncing) {
                EmailUIUtils.showSyncStatus(this.inbox.elements, `同步進行中... ${result.data.progress || ''}%`);
                
                // 持續檢查直到同步完成
                setTimeout(() => {
                    this.checkSyncStatus();
                }, 2000);
            } else {
                EmailUIUtils.hideSyncStatus(this.inbox.elements);
            }
            
        } catch (error) {
            console.error('檢查同步狀態失敗:', error);
        }
    }
}

// 全域可用
window.EmailOperations = EmailOperations;
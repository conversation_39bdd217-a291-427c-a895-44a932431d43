/**
 * 郵件解析 UI 增強
 * 處理解析結果的顯示和互動
 */

class EmailParserUI {
    constructor() {
        this.initialized = false;
    }
    
    /**
     * 初始化解析 UI
     */
    initialize() {
        if (this.initialized) return;
        
        // 添加解析相關的樣式
        this.injectStyles();
        
        // 修改郵件列表顯示
        this.enhanceEmailList();
        
        this.initialized = true;
    }
    
    /**
     * 注入解析相關的樣式
     */
    injectStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .email-parse-info {
                display: flex;
                gap: 10px;
                align-items: center;
                font-size: 0.85em;
                color: #666;
            }
            
            .parse-tag {
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.8em;
                white-space: nowrap;
            }
            
            .parse-tag.vendor {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            
            .parse-tag.lot {
                background-color: #f3e5f5;
                color: #7b1fa2;
            }
            
            .parse-tag.yield {
                background-color: #e8f5e9;
                color: #388e3c;
            }
            
            .parse-tag.pd {
                background-color: #fff3e0;
                color: #f57c00;
            }
            
            .parse-tag.traditional {
                background-color: #e8eaf6;
                color: #3f51b5;
            }
            
            .parse-tag.llm {
                background-color: #f1f8e9;
                color: #689f38;
            }
            
            .parse-tag.hybrid {
                background-color: #fce4ec;
                color: #ad1457;
            }
            
            .parse-tag.fallback {
                background-color: #fff8e1;
                color: #f9a825;
            }
            
            .parse-status {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                display: inline-block;
                margin-right: 5px;
            }
            
            .parse-status.parsed {
                background-color: #4caf50;
            }
            
            .parse-status.failed {
                background-color: #f44336;
            }
            
            .parse-status.pending {
                background-color: #ff9800;
            }
            
            .email-item.has-parse-data {
                background-color: #f5f5f5;
            }
            
            .parse-actions {
                display: flex;
                gap: 5px;
            }
            
            .parse-btn {
                padding: 4px 8px;
                font-size: 0.8em;
                background-color: #2196f3;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            
            .parse-btn:hover {
                background-color: #1976d2;
            }
            
            /* 批次解析對話框樣式 */
            .batch-parse-options {
                margin: 20px 0;
            }
            
            .radio-option {
                display: block;
                margin: 10px 0;
                padding: 10px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            
            .radio-option:hover {
                background-color: #f5f5f5;
            }
            
            .radio-option input[type="radio"] {
                margin-right: 10px;
            }
            
            .parse-count {
                color: #666;
                font-size: 0.9em;
            }
            
            .batch-parse-info {
                margin-top: 20px;
                padding: 10px;
                background-color: #e3f2fd;
                border-radius: 4px;
            }
            
            .info-text {
                margin: 0;
                color: #1976d2;
                font-size: 0.9em;
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 增強郵件列表顯示
     */
    enhanceEmailList() {
        // 覆寫原有的 createEmailItem 方法
        if (window.emailInbox && window.emailInbox.listManager) {
            const originalCreateEmailItem = window.emailInbox.listManager.createEmailItem.bind(window.emailInbox.listManager);
            
            window.emailInbox.listManager.createEmailItem = (email) => {
                return this.enhanceEmailItem(originalCreateEmailItem(email), email);
            };
        }
    }
    
    /**
     * 增強單個郵件項目
     */
    enhanceEmailItem(baseHtml, email) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = baseHtml;
        const emailItem = tempDiv.firstElementChild;
        
        // 添加解析狀態類別
        if (email.parse_status === 'parsed') {
            emailItem.classList.add('has-parse-data');
        }
        
        // 在主題後添加解析資訊
        const subjectDiv = emailItem.querySelector('.email-subject');
        if (subjectDiv) {
            const parseInfo = this.createParseInfo(email);
            subjectDiv.insertAdjacentHTML('afterend', parseInfo);
        }
        
        // 在操作按鈕中添加解析按鈕
        const actionsDiv = emailItem.querySelector('.email-actions');
        if (actionsDiv && email.parse_status !== 'parsed') {
            const parseBtn = `
                <button class="parse-btn" onclick="window.emailParserUI.parseEmail(${email.id})" title="解析郵件">
                    🔍
                </button>
            `;
            actionsDiv.insertAdjacentHTML('afterbegin', parseBtn);
        }
        
        return emailItem.outerHTML;
    }
    
    /**
     * 創建解析資訊顯示
     */
    createParseInfo(email) {
        let infoHtml = '<div class="email-parse-info">';
        
        // 解析狀態指示器
        infoHtml += `<span class="parse-status ${email.parse_status}" title="${this.getParseStatusText(email.parse_status)}"></span>`;
        
        // 解析結果標籤
        if (email.parse_status === 'parsed') {
            if (email.vendor_code) {
                infoHtml += `<span class="parse-tag vendor">${email.vendor_code}</span>`;
            }
            if (email.pd) {
                infoHtml += `<span class="parse-tag pd" title="產品: ${email.pd}">PD: ${this.truncate(email.pd, 10)}</span>`;
            }
            if (email.lot) {
                infoHtml += `<span class="parse-tag lot" title="批號: ${email.lot}">LOT: ${this.truncate(email.lot, 10)}</span>`;
            }
            if (email.yield_value) {
                infoHtml += `<span class="parse-tag yield">良率: ${email.yield_value}</span>`;
            }
            // 添加解析方法標籤
            if (email.extraction_method) {
                const methodText = this.getExtractionMethodText(email.extraction_method);
                const methodClass = this.getExtractionMethodClass(email.extraction_method);
                infoHtml += `<span class="parse-tag ${methodClass}" title="解析方法: ${methodText}">${methodText}</span>`;
            } else {
                // 暫時顯示默認的解析方法標籤
                if (email.vendor_code) {
                    infoHtml += `<span class="parse-tag fallback" title="解析方法: 備援">備援</span>`;
                }
            }
        } else if (email.parse_status === 'failed' && email.parse_error) {
            infoHtml += `<span class="parse-tag" style="background-color: #ffebee; color: #c62828;" title="${email.parse_error}">解析失敗</span>`;
        }
        
        infoHtml += '</div>';
        return infoHtml;
    }
    
    /**
     * 取得解析狀態文字
     */
    getParseStatusText(status) {
        const statusMap = {
            'parsed': '已解析',
            'pending': '待解析',
            'failed': '解析失敗'
        };
        return statusMap[status] || status;
    }
    
    /**
     * 取得解析方法文字
     */
    getExtractionMethodText(method) {
        const methodMap = {
            'traditional': '傳統',
            'llm': 'LLM',
            'hybrid': '混合',
            'fallback': '備援'
        };
        return methodMap[method] || method;
    }
    
    /**
     * 取得解析方法CSS類別
     */
    getExtractionMethodClass(method) {
        const classMap = {
            'traditional': 'traditional',
            'llm': 'llm', 
            'hybrid': 'hybrid',
            'fallback': 'fallback'
        };
        return classMap[method] || 'traditional';
    }
    
    /**
     * 截斷文字
     */
    truncate(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    /**
     * 解析單個郵件
     */
    async parseEmail(emailId) {
        try {
            const response = await fetch(`/api/parser/emails/${emailId}/reparse`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('郵件解析成功', 'success');
                // 重新載入郵件列表
                if (window.emailInbox && window.emailInbox.listManager) {
                    window.emailInbox.listManager.loadEmails();
                }
            } else {
                this.showNotification('郵件解析失敗: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('解析郵件失敗:', error);
            this.showNotification('解析郵件失敗', 'error');
        }
    }
    
    /**
     * 顯示批次解析對話框
     */
    async showBatchParseDialog() {
        const dialog = document.getElementById('batch-parse-dialog');
        if (!dialog) return;
        
        // 先獲取統計資訊
        try {
            const response = await fetch('/api/parser/statistics');
            const result = await response.json();
            
            if (result.success && result.data.database_stats) {
                const stats = result.data.database_stats;
                const pendingCount = document.getElementById('pending-count');
                const failedCount = document.getElementById('failed-count');
                
                if (pendingCount) {
                    pendingCount.textContent = `(${stats.pending_emails || 0} 封)`;
                }
                if (failedCount) {
                    failedCount.textContent = `(${stats.failed_emails || 0} 封)`;
                }
            }
        } catch (error) {
            console.error('獲取解析統計失敗:', error);
        }
        
        dialog.classList.remove('hidden');
    }
    
    /**
     * 隱藏批次解析對話框
     */
    hideBatchParseDialog() {
        const dialog = document.getElementById('batch-parse-dialog');
        if (dialog) {
            dialog.classList.add('hidden');
        }
    }
    
    /**
     * 執行批次解析
     */
    async executeBatchParse() {
        const selectedType = document.querySelector('input[name="parse-type"]:checked');
        if (!selectedType) {
            this.showNotification('請選擇解析類型', 'warning');
            return;
        }
        
        this.hideBatchParseDialog();
        
        // 根據選擇的類型調用不同的解析方法
        switch (selectedType.value) {
            case 'pending':
                await this.batchParseEmails(false);
                break;
            case 'failed':
                await this.batchParseEmails(true);
                break;
            case 'all':
                // 先解析待解析的，再解析失敗的
                await this.batchParseEmails(false);
                await this.batchParseEmails(true);
                break;
        }
    }
    
    /**
     * 自動解析所有未解析郵件
     */
    async autoParseAllPending() {
        try {
            // 直接執行批次解析，不顯示對話框
            await this.batchParseEmails(false);
        } catch (error) {
            console.error('自動批次解析失敗:', error);
        }
    }
    
    /**
     * 批次處理已解析郵件的檔案
     */
    async batchProcessFiles() {
        try {
            // 顯示處理中提示
            this.showNotification('正在批次處理已解析郵件的檔案...', 'info');
            
            const response = await fetch('/api/parser/emails/batch-process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                if (result.processed_count === 0) {
                    this.showNotification('沒有已解析的郵件需要處理', 'info');
                } else {
                    this.showNotification(
                        `批次處理完成: 處理 ${result.processed_count} 封郵件，成功 ${result.success_count}，失敗 ${result.failed_count}`, 
                        'success'
                    );
                }
                
                // 重新載入郵件列表
                if (window.emailInbox && window.emailInbox.listManager) {
                    window.emailInbox.listManager.loadEmails();
                }
            } else {
                this.showNotification('批次處理失敗: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('批次處理失敗:', error);
            this.showNotification('批次處理失敗', 'error');
        }
    }
    
    /**
     * 批次解析郵件
     */
    async batchParseEmails(onlyFailed = false) {
        try {
            // 顯示進度提示
            this.showNotification(
                `正在批次解析${onlyFailed ? '失敗' : '待解析'}郵件...`, 
                'info'
            );
            
            const response = await fetch('/api/parser/emails/batch-parse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    failed_only: onlyFailed,
                    limit: 50
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                if (result.parsed_count === 0) {
                    this.showNotification(
                        `沒有${onlyFailed ? '失敗' : '待解析'}的郵件需要處理`, 
                        'info'
                    );
                } else {
                    this.showNotification(
                        `批次解析完成: 處理 ${result.parsed_count} 封，成功 ${result.success_count}，失敗 ${result.failed_count}`, 
                        'success'
                    );
                }
                
                // 重新載入郵件列表
                if (window.emailInbox && window.emailInbox.listManager) {
                    window.emailInbox.listManager.loadEmails();
                }
            } else {
                this.showNotification('批次解析失敗: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('批次解析失敗:', error);
            this.showNotification('批次解析失敗', 'error');
        }
    }
    
    /**
     * 顯示通知
     */
    showNotification(message, type = 'info') {
        if (window.EmailUIUtils) {
            const elements = window.emailInbox ? window.emailInbox.elements : {};
            window.EmailUIUtils.showNotification(elements, message, type);
        } else {
            alert(message);
        }
    }
}

// 創建全域實例
window.emailParserUI = new EmailParserUI();

// 在 DOM 載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待郵件收件夾初始化完成
    setTimeout(() => {
        window.emailParserUI.initialize();
        
        // 自動執行批次解析（解析所有未解析郵件）
        setTimeout(() => {
            console.log('自動執行批次解析所有未解析郵件...');
            window.emailParserUI.autoParseAllPending();
        }, 500);
    }, 1000);
});
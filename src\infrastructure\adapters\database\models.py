"""
郵件資料庫模型
使用 SQLAlchemy 定義郵件相關的資料庫表結構
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, 
    ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy import create_engine

Base = declarative_base()


class EmailDB(Base):
    """郵件資料庫模型"""
    __tablename__ = 'emails'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    message_id = Column(String(255), unique=True, nullable=False, index=True)
    sender = Column(String(255), nullable=False, index=True)
    sender_display_name = Column(String(255), nullable=True)
    subject = Column(Text, nullable=False)
    body = Column(Text, nullable=True)
    received_time = Column(DateTime, nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    is_read = Column(Boolean, default=False, nullable=False)
    is_processed = Column(Boolean, default=False, nullable=False)
    has_attachments = Column(Boolean, default=False, nullable=False)
    attachment_count = Column(Integer, default=0, nullable=False)
    
    # 解析相關欄位
    pd = Column(String(100), nullable=True, index=True)
    lot = Column(String(100), nullable=True, index=True)
    yield_value = Column(String(50), nullable=True)
    vendor_code = Column(String(50), nullable=True, index=True)
    parsed_at = Column(DateTime, nullable=True)
    parse_status = Column(String(20), default='pending')  # pending, parsed, failed
    parse_error = Column(Text, nullable=True)
    extraction_method = Column(String(50), nullable=True)  # traditional, llm, hybrid, fallback
    
    # 關聯
    attachments = relationship("AttachmentDB", back_populates="email", cascade="all, delete-orphan")
    process_status = relationship("EmailProcessStatusDB", back_populates="email", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_email_sender_time', 'sender', 'received_time'),
        Index('idx_email_subject', 'subject'),
        Index('idx_email_created', 'created_at'),
        Index('idx_email_parse_status', 'parse_status'),
        Index('idx_email_vendor', 'vendor_code'),
    )
    
    def __repr__(self):
        return f"<EmailDB(id={self.id}, subject='{self.subject[:50]}...', sender='{self.sender}')>"


class SenderDB(Base):
    """寄件者資料庫模型"""
    __tablename__ = 'senders'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    email_address = Column(String(255), unique=True, nullable=False, index=True)
    display_name = Column(String(255), nullable=True)
    total_emails = Column(Integer, default=0, nullable=False)
    last_email_time = Column(DateTime, nullable=True)
    first_email_time = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f"<SenderDB(email='{self.email_address}', total_emails={self.total_emails})>"


class AttachmentDB(Base):
    """附件資料庫模型"""
    __tablename__ = 'attachments'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    email_id = Column(Integer, ForeignKey('emails.id'), nullable=False, index=True)
    filename = Column(String(255), nullable=False)
    content_type = Column(String(100), nullable=True)
    size_bytes = Column(Integer, nullable=False)
    file_path = Column(Text, nullable=True)
    checksum = Column(String(64), nullable=True)
    is_processed = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # 關聯
    email = relationship("EmailDB", back_populates="attachments")
    
    # 索引
    __table_args__ = (
        Index('idx_attachment_email', 'email_id'),
        Index('idx_attachment_filename', 'filename'),
    )
    
    def __repr__(self):
        return f"<AttachmentDB(filename='{self.filename}', size={self.size_bytes})>"


class EmailProcessStatusDB(Base):
    """郵件處理狀態資料庫模型"""
    __tablename__ = 'email_process_status'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    email_id = Column(Integer, ForeignKey('emails.id'), nullable=False, index=True)
    step_name = Column(String(100), nullable=False)  # eqctotaldata, code_detection, dual_search, report_generation
    status = Column(String(20), default='pending', nullable=False)  # pending, processing, completed, failed
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    output_files = Column(Text, nullable=True)  # JSON 格式儲存輸出檔案路徑
    progress_percentage = Column(Integer, default=0, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 關聯
    email = relationship("EmailDB", back_populates="process_status")
    
    # 索引和約束
    __table_args__ = (
        Index('idx_process_email_step', 'email_id', 'step_name'),
        Index('idx_process_status', 'status'),
        UniqueConstraint('email_id', 'step_name', name='uq_email_step'),
    )
    
    def __repr__(self):
        return f"<EmailProcessStatusDB(email_id={self.email_id}, step='{self.step_name}', status='{self.status}')>"


class DatabaseEngine:
    """資料庫引擎管理器"""
    
    def __init__(self, database_url: str = "sqlite:///email_inbox.db"):
        self.database_url = database_url
        self.engine = None
        self.SessionLocal = None
    
    def initialize(self):
        """初始化資料庫引擎和會話"""
        self.engine = create_engine(
            self.database_url,
            echo=False,  # 設為 True 可以看到 SQL 查詢
            pool_pre_ping=True,
            connect_args={"check_same_thread": False} if "sqlite" in self.database_url else {}
        )
        
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # 創建所有表
        Base.metadata.create_all(bind=self.engine)
    
    def get_session(self):
        """取得資料庫會話"""
        if not self.SessionLocal:
            self.initialize()
        return self.SessionLocal()
    
    def close(self):
        """關閉資料庫連接"""
        if self.engine:
            self.engine.dispose()


# 預設資料庫引擎實例
db_engine = DatabaseEngine()
"""
Ollama LLM 客戶端
提供郵件解析的 LLM 服務接口
"""

import json
import requests
from typing import Dict, Any, Optional, List
import os
from dataclasses import dataclass
import time

from src.infrastructure.logging.logger_manager import LoggerManager


@dataclass
class LLMParsingResult:
    """LLM 解析結果"""
    vendor_code: Optional[str] = None
    vendor_name: Optional[str] = None
    product_code: Optional[str] = None
    lot_number: Optional[str] = None
    mo_number: Optional[str] = None
    yield_rate: Optional[float] = None
    test_batch: Optional[str] = None
    product_description: Optional[str] = None
    confidence_score: float = 0.0
    extraction_method: str = "llm"
    raw_response: Optional[str] = None
    error_message: Optional[str] = None
    is_success: bool = False


class OllamaClient:
    """Ollama LLM 客戶端"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("OllamaClient")
        
        # 從環境變數載入配置
        self.base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
        self.model = os.getenv('OLLAMA_MODEL', 'llama3:latest')
        self.timeout = int(os.getenv('LLM_TIMEOUT', '30'))
        self.max_retries = int(os.getenv('LLM_MAX_RETRIES', '3'))
        self.confidence_threshold = float(os.getenv('LLM_CONFIDENCE_THRESHOLD', '0.7'))
        
        self.logger.info(f"Ollama 客戶端初始化: {self.base_url}, 模型: {self.model}")
    
    def test_connection(self) -> bool:
        """測試 Ollama 服務連接"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [m['name'] for m in models]
                
                if self.model in model_names:
                    self.logger.info(f"Ollama 連接成功，模型 {self.model} 可用")
                    return True
                else:
                    self.logger.warning(f"模型 {self.model} 不存在，可用模型: {model_names}")
                    return False
            else:
                self.logger.error(f"Ollama 服務回應錯誤: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"Ollama 連接失敗: {e}")
            return False
    
    def _call_ollama(self, prompt: str) -> Optional[str]:
        """調用 Ollama API"""
        url = f"{self.base_url}/api/generate"
        
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,  # 低溫度確保一致性
                "top_p": 0.9,
                "num_predict": 200   # 限制回應長度
            }
        }
        
        for attempt in range(self.max_retries):
            try:
                self.logger.debug(f"調用 Ollama API (嘗試 {attempt + 1}/{self.max_retries})")
                
                response = requests.post(
                    url, 
                    json=payload, 
                    timeout=self.timeout,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get('response', '').strip()
                else:
                    self.logger.warning(f"Ollama API 錯誤 {response.status_code}: {response.text}")
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"Ollama API 超時 (嘗試 {attempt + 1}/{self.max_retries})")
            except Exception as e:
                self.logger.error(f"Ollama API 調用失敗: {e}")
                
            if attempt < self.max_retries - 1:
                time.sleep(2 ** attempt)  # 指數退避
        
        return None
    
    def parse_email(self, subject: str, body: str = "") -> LLMParsingResult:
        """使用 LLM 解析郵件"""
        try:
            # 構建解析提示
            prompt = self._build_parsing_prompt(subject, body)
            
            # 調用 LLM
            response = self._call_ollama(prompt)
            
            if not response:
                return LLMParsingResult(
                    error_message="LLM API 調用失敗",
                    is_success=False
                )
            
            # 解析 LLM 回應
            result = self._parse_llm_response(response)
            result.raw_response = response
            
            return result
            
        except Exception as e:
            self.logger.error(f"郵件解析失敗: {e}")
            return LLMParsingResult(
                error_message=str(e),
                is_success=False
            )
    
    def _build_parsing_prompt(self, subject: str, body: str = "") -> str:
        """構建解析提示"""
        content = f"主旨: {subject}"
        if body.strip():
            content += f"\n內容: {body}"
        
        prompt = f"""Parse this email subject and extract fields as JSON:

Subject: {subject}

Extract:
- vendor_code: Identify the vendor based on these patterns:
  * GTK: If subject contains "FT Hold", "Low yield", or GTK patterns
  * JCET: If subject contains "Taiwan", "致新", "铜丝", or JCET patterns  
  * ETD: If subject contains ETD patterns
  * LINGSEN: If subject contains LINGSEN patterns
  * XAHT: If subject contains XAHT patterns
- product_code: Product model/type
- lot_number: LOT number
- mo_number: MO number  
- yield_rate: Yield percentage as number only
- test_batch: Test batch number

JSON format:
{{"vendor_code":"VALUE","product_code":"VALUE","lot_number":"VALUE","mo_number":"VALUE","yield_rate":NUMBER,"test_batch":"VALUE"}}"""
        return prompt
    
    def _parse_llm_response(self, response: str) -> LLMParsingResult:
        """解析 LLM 回應"""
        try:
            # 嘗試提取 JSON
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                self.logger.warning("LLM 回應中未找到 JSON 格式")
                return LLMParsingResult(
                    error_message="回應格式錯誤",
                    is_success=False
                )
            
            json_str = response[json_start:json_end]
            data = json.loads(json_str)
            
            # 構建結果
            result = LLMParsingResult(
                vendor_code=data.get('vendor_code'),
                product_code=data.get('product_code'),
                lot_number=data.get('lot_number'),
                mo_number=data.get('mo_number'),
                yield_rate=data.get('yield_rate'),
                test_batch=data.get('test_batch'),
                product_description=data.get('product_description'),
                confidence_score=data.get('confidence', 0.8),  # 預設信心分數
                extraction_method="llm"
            )
            
            # 計算信心分數（基於提取到的字段數量）
            fields_extracted = sum(1 for field in [result.vendor_code, result.product_code, 
                                                 result.lot_number, result.yield_rate] if field is not None)
            result.confidence_score = min(0.9, 0.5 + (fields_extracted * 0.1))
            
            # 驗證結果
            if result.vendor_code and result.confidence_score >= self.confidence_threshold:
                result.is_success = True
                # 設定廠商名稱
                vendor_names = {
                    'GTK': 'GTK',
                    'ETD': 'ETD', 
                    'JCET': 'JCET',
                    'LINGSEN': 'LINGSEN',
                    'XAHT': 'XAHT'
                }
                result.vendor_name = vendor_names.get(result.vendor_code, result.vendor_code)
            else:
                result.is_success = False
                result.error_message = f"信心分數過低: {result.confidence_score}"
            
            return result
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON 解析失敗: {e}")
            self.logger.debug(f"原始回應: {response}")
            return LLMParsingResult(
                error_message=f"JSON 解析錯誤: {e}",
                is_success=False
            )
        except Exception as e:
            self.logger.error(f"回應解析失敗: {e}")
            return LLMParsingResult(
                error_message=str(e),
                is_success=False
            )
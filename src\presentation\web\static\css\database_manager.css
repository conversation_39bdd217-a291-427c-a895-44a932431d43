/* 資料庫管理頁面樣式 */

.db-manager-container {
    min-height: 100vh;
    background-color: #f5f7fa;
}

/* 頂部標題 */
.db-header {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-left h1 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.db-info {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: #666;
}

.info-item strong {
    color: #333;
}

/* 表格選擇器 */
.table-selector {
    background-color: white;
    padding: 1.5rem 2rem;
    margin: 1rem 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.form-select {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    min-width: 300px;
}

/* 表格資訊 */
.table-info {
    background-color: white;
    padding: 1rem 2rem;
    margin: 0 2rem 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-info h3 {
    margin: 0 0 0.5rem;
    color: #333;
}

.table-stats {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: #666;
}

.table-stats strong {
    color: #333;
}

/* SQL 查詢區 */
.sql-query-section {
    background-color: white;
    padding: 1.5rem 2rem;
    margin: 0 2rem 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sql-query-section h3 {
    margin: 0 0 1rem;
    color: #333;
}

.query-container {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

#sql-query {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    resize: vertical;
    min-height: 60px;
}

#execute-query-btn {
    flex-shrink: 0;
}

.error-message {
    color: #d9534f;
    padding: 0.5rem;
    margin-top: 0.5rem;
    background-color: #f2dede;
    border: 1px solid #ebccd1;
    border-radius: 4px;
}

/* 資料容器 */
.data-container {
    background-color: white;
    padding: 1.5rem;
    margin: 0 2rem 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-height: 300px;
    overflow-x: auto;
}

/* 載入動畫 */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4285f4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* DataTable 樣式覆蓋 */
.dataTable {
    font-size: 0.9rem;
    border-collapse: collapse;
    width: 100%;
}

.dataTable th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
    border: 1px solid #dee2e6;
    padding: 12px 8px;
    text-align: left;
}

.dataTable td {
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 8px;
    background-color: #fff;
}

/* 斑馬紋效果 */
.dataTable tbody tr:nth-child(even) td {
    background-color: #f9f9f9;
}

/* 滑鼠懸停效果 */
.dataTable tbody tr:hover td {
    background-color: #e3f2fd;
}

/* 移除表格標題固定效果，讓標題跟著滾動 */
.dataTable thead th {
    /* position: sticky; */
    /* top: 0; */
    z-index: 10;
    box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
}

/* 分頁控制樣式 */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: #fff;
    color: #495057;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #e9ecef;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

/* 搜尋框樣式 */
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 每頁顯示數量選擇框 */
.dataTables_wrapper .dataTables_length select {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 表格資訊顯示 */
.dataTables_wrapper .dataTables_info {
    padding-top: 0.75rem;
    color: #6c757d;
    font-size: 0.875rem;
}

.null-value {
    color: #999;
    font-style: italic;
}

.btn-view-detail {
    padding: 0.25rem 0.75rem;
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    margin-right: 5px;
}

.btn-view-detail:hover {
    background-color: #357ae8;
}

.btn-delete-row {
    padding: 0.25rem 0.75rem;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
}

.btn-delete-row:hover {
    background-color: #c82333;
}

/* 選擇欄位樣式 */
.select-checkbox {
    text-align: center;
    width: 50px;
}

.select-checkbox input[type="checkbox"] {
    transform: scale(1.2);
    cursor: pointer;
}

/* 模態框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #ddd;
    text-align: right;
}

/* 詳情表格 */
.detail-table {
    width: 100%;
    border-collapse: collapse;
}

.detail-table tr {
    border-bottom: 1px solid #eee;
}

.detail-table td {
    padding: 0.75rem;
    vertical-align: top;
}

.detail-key {
    font-weight: 600;
    color: #666;
    width: 30%;
    background-color: #f8f9fa;
}

.detail-value {
    color: #333;
    word-break: break-word;
}

.detail-value pre {
    margin: 0;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
}

/* LLM 分析結果樣式 */
.llm-analysis-container {
    padding: 0;
}

.llm-analysis-container .section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.llm-analysis-container .section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1em;
    font-weight: 600;
}

.llm-analysis-container .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
}

.llm-analysis-container .info-item {
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.llm-analysis-container .info-item strong {
    color: #495057;
    font-weight: 600;
}

.llm-analysis-container .llm-status {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.llm-analysis-container .status-badge {
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
}

.llm-analysis-container .status-badge.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.llm-analysis-container .status-badge.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.llm-analysis-container .confidence-score {
    padding: 3px 8px;
    background-color: #e3f2fd;
    color: #1565c0;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 500;
}

.llm-analysis-container .raw-response {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.llm-analysis-container .raw-response pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.llm-analysis-container .error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
    margin-top: 15px;
}

/* 隱藏類 */
.hidden {
    display: none !important;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .db-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .table-selector {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-select {
        width: 100%;
    }
    
    .query-container {
        flex-direction: column;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}
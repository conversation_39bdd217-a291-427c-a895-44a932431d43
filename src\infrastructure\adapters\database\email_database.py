"""
郵件資料庫操作類
提供郵件資料的增刪查改功能
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, and_, or_
from contextlib import contextmanager
import json
import email.header
from email.utils import parseaddr

from .models import EmailDB, SenderDB, AttachmentDB, EmailProcessStatusDB, db_engine
from src.data_models.email_models import EmailData, EmailAttachment
from src.infrastructure.logging.logger_manager import LoggerManager


class EmailDatabase:
    """
    郵件資料庫操作類
    提供郵件相關的資料庫操作功能
    """
    
    def __init__(self, database_url: str = None):
        """
        初始化郵件資料庫
        
        Args:
            database_url: 資料庫連接 URL，None 表示使用預設值
        """
        self.logger = LoggerManager().get_logger("EmailDatabase")
        
        if database_url:
            db_engine.database_url = database_url
        
        # 初始化資料庫
        db_engine.initialize()
        
        self.logger.info(f"郵件資料庫已初始化: {db_engine.database_url}")
    
    def decode_mime_header(self, header_value):
        """解碼 MIME 編碼的郵件頭"""
        if not header_value:
            return header_value
            
        try:
            # 解碼 MIME 編碼
            decoded_parts = email.header.decode_header(header_value)
            result = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        try:
                            result += part.decode(encoding)
                        except (UnicodeDecodeError, LookupError) as e:
                            # 記錄編碼失敗的詳細信息
                            self.logger.warning(f"指定編碼 '{encoding}' 解碼失敗: {e}, 嘗試備用編碼")
                            
                            # 如果指定編碼失敗，嘗試常見編碼
                            decoded = False
                            for fallback_encoding in ['utf-8', 'big5', 'gbk', 'cp950', 'latin-1']:
                                try:
                                    decoded_text = part.decode(fallback_encoding)
                                    result += decoded_text
                                    self.logger.debug(f"使用備用編碼 '{fallback_encoding}' 成功解碼")
                                    decoded = True
                                    break
                                except (UnicodeDecodeError, LookupError):
                                    continue
                            
                            if not decoded:
                                # 所有編碼都失敗，使用 replace 模式
                                fallback_text = part.decode('utf-8', errors='replace')
                                result += fallback_text
                                self.logger.warning(f"所有編碼都失敗，使用 UTF-8 replace 模式: 原始長度={len(part)}")
                    else:
                        try:
                            result += part.decode('utf-8')
                        except UnicodeDecodeError as e:
                            self.logger.warning(f"UTF-8 解碼失敗: {e}, 使用 replace 模式")
                            result += part.decode('utf-8', errors='replace')
                else:
                    result += str(part)
            
            # 清理結果
            result = result.strip()
            
            # 檢查結果是否合理
            if len(result) > 1000:  # 如果解碼後的結果過長，可能有問題
                self.logger.warning(f"解碼後的標題過長: {len(result)} 字符，原始: {header_value[:100]}...")
            
            return result
            
        except Exception as e:
            self.logger.error(f"解碼 MIME 頭失敗: {e}, 原始值: {header_value}")
            # 返回原始值或安全的替代值
            return str(header_value) if header_value else ""
    
    def decode_sender(self, sender_value):
        """解碼發件人信息"""
        if not sender_value:
            return sender_value
            
        try:
            # <AUTHOR> <EMAIL>
            name, email_addr = parseaddr(sender_value)
            
            # 解碼姓名部分
            if name:
                try:
                    decoded_name = self.decode_mime_header(name)
                    if email_addr:
                        return f"{decoded_name} <{email_addr}>"
                    else:
                        return decoded_name
                except Exception as e:
                    self.logger.warning(f"解碼寄件者姓名失敗: {e}, 使用原始姓名")
                    # 如果姓名解碼失敗，使用原始姓名
                    if email_addr:
                        return f"{name} <{email_addr}>"
                    else:
                        return name
            else:
                # 沒有姓名，只有郵件地址
                return email_addr or sender_value
                
        except Exception as e:
            self.logger.error(f"解碼發件人失敗: {e}, 原始值: {sender_value}")
            # 解析失敗，返回原始值
            return str(sender_value) if sender_value else ""
    
    @contextmanager
    def get_session(self):
        """取得資料庫會話的上下文管理器"""
        session = db_engine.get_session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"資料庫操作失敗: {e}")
            raise
        finally:
            session.close()
    
    def save_email(self, email_data: EmailData) -> Optional[int]:
        """
        儲存郵件到資料庫
        
        Args:
            email_data: 郵件數據對象
            
        Returns:
            儲存的郵件 ID，如果失敗則返回 None
        """
        try:
            with self.get_session() as session:
                # 檢查郵件是否已存在
                existing_email = session.query(EmailDB).filter_by(
                    message_id=email_data.message_id
                ).first()
                
                if existing_email:
                    self.logger.debug(f"郵件已存在: {email_data.message_id}")
                    return existing_email.id
                
                # 記錄郵件基本信息用於診斷
                self.logger.debug(f"正在儲存郵件: subject='{email_data.subject[:50]}...', sender='{email_data.sender}', message_id='{email_data.message_id}'")
                
                # 檢查必填欄位
                if not email_data.message_id:
                    self.logger.error(f"郵件 message_id 為空: subject='{email_data.subject}', sender='{email_data.sender}'")
                    return None
                
                if not email_data.sender:
                    self.logger.error(f"郵件寄件者為空: message_id='{email_data.message_id}', subject='{email_data.subject}'")
                    return None
                
                # 處理欄位長度限制 (根據資料庫模型)
                message_id = email_data.message_id
                sender = email_data.sender or ""
                subject = email_data.subject or ""
                
                # Message ID 限制為 255 字符
                if len(message_id) > 255:
                    self.logger.warning(f"Message ID 過長，將被截斷: 原長度={len(message_id)}")
                    message_id = message_id[:255]
                
                # 寄件者限制為 255 字符  
                if len(sender) > 255:
                    self.logger.warning(f"寄件者名稱過長，將被截斷: 原長度={len(sender)}")
                    sender = sender[:255]
                
                # Subject 使用 Text 類型，但為了安全起見限制在合理範圍
                if len(subject) > 10000:  # 10KB 限制
                    self.logger.warning(f"郵件主題過長，將被截斷: 原長度={len(subject)}")
                    subject = subject[:10000]
                
                # 處理郵件內容
                body = email_data.body or ""
                if len(body) > 1000000:  # 1MB 限制
                    self.logger.warning(f"郵件內容過長，將被截斷: 原長度={len(body)}")
                    body = body[:1000000]
                
                # 處理寄件者顯示名稱
                sender_display_name = getattr(email_data, 'sender_display_name', None)
                if sender_display_name and len(sender_display_name) > 255:
                    self.logger.warning(f"寄件者顯示名稱過長，將被截斷: 原長度={len(sender_display_name)}")
                    sender_display_name = sender_display_name[:255]
                
                # 建立郵件記錄
                email_db = EmailDB(
                    message_id=message_id,
                    sender=sender,
                    sender_display_name=sender_display_name,
                    subject=subject,
                    body=body,
                    received_time=email_data.received_time,
                    has_attachments=len(email_data.attachments) > 0,
                    attachment_count=len(email_data.attachments)
                )
                
                try:
                    session.add(email_db)
                    session.flush()  # 取得 ID
                    
                    # 儲存附件
                    if email_data.attachments:
                        for i, attachment in enumerate(email_data.attachments):
                            try:
                                # 檢查附件資料
                                filename = attachment.filename or f"unnamed_attachment_{i+1}"
                                if len(filename) > 255:
                                    self.logger.warning(f"附件檔名過長，將被截斷: {filename}")
                                    filename = filename[:255]
                                
                                content_type = attachment.content_type or "application/octet-stream"
                                if len(content_type) > 100:
                                    content_type = content_type[:100]
                                
                                attachment_db = AttachmentDB(
                                    email_id=email_db.id,
                                    filename=filename,
                                    content_type=content_type,
                                    size_bytes=attachment.size_bytes or 0,
                                    file_path=str(attachment.file_path) if attachment.file_path else None
                                )
                                session.add(attachment_db)
                            except Exception as e:
                                self.logger.error(f"儲存附件 {i+1} 失敗: filename='{attachment.filename}', error={e}")
                                # 繼續處理其他附件
                    
                    # 更新或創建寄件者記錄
                    try:
                        self._update_sender_stats(session, email_data.sender, email_data.received_time, sender_display_name)
                    except Exception as e:
                        self.logger.error(f"更新寄件者統計失敗: sender='{email_data.sender}', error={e}")
                        # 不影響郵件儲存
                    
                    session.commit()
                    
                    self.logger.info(f"郵件已儲存: {email_data.subject} (ID: {email_db.id})")
                    return email_db.id
                    
                except Exception as e:
                    session.rollback()
                    
                    # 分析具體的資料庫錯誤
                    error_msg = str(e).lower()
                    if "unique constraint" in error_msg or "duplicate" in error_msg:
                        self.logger.error(f"郵件重複 (唯一約束衝突): message_id='{message_id}', error={e}")
                    elif "not null" in error_msg:
                        self.logger.error(f"必填欄位為空: message_id='{message_id}', error={e}")
                    elif "foreign key" in error_msg:
                        self.logger.error(f"外鍵約束失敗: message_id='{message_id}', error={e}")
                    else:
                        self.logger.error(f"資料庫操作失敗: message_id='{message_id}', subject='{subject[:50]}', sender='{sender[:50]}', error={e}")
                    
                    return None
                
        except Exception as e:
            self.logger.error(f"儲存郵件失敗: message_id='{getattr(email_data, 'message_id', 'N/A')}', subject='{getattr(email_data, 'subject', 'N/A')[:50]}', sender='{getattr(email_data, 'sender', 'N/A')[:50]}', error={e}")
            return None
    
    def get_emails(self, 
                   sender: Optional[str] = None,
                   limit: int = 50,
                   offset: int = 0,
                   order_by: str = 'received_time',
                   order_desc: bool = True) -> List[Dict[str, Any]]:
        """
        查詢郵件列表
        
        Args:
            sender: 寄件者篩選
            limit: 限制數量
            offset: 偏移量
            order_by: 排序欄位
            order_desc: 是否降序
            
        Returns:
            郵件列表
        """
        try:
            with self.get_session() as session:
                query = session.query(EmailDB)
                
                # 寄件者篩選
                if sender:
                    query = query.filter(EmailDB.sender == sender)
                
                # 排序
                order_field = getattr(EmailDB, order_by, EmailDB.received_time)
                if order_desc:
                    query = query.order_by(desc(order_field))
                else:
                    query = query.order_by(asc(order_field))
                
                # 分頁
                emails = query.offset(offset).limit(limit).all()
                
                # 轉換為字典格式
                result = []
                for email in emails:
                    email_dict = {
                        'id': email.id,
                        'message_id': email.message_id,
                        'sender': self.decode_sender(email.sender),
                        'subject': self.decode_mime_header(email.subject),
                        'body': email.body,
                        'received_time': email.received_time.isoformat(),
                        'created_at': email.created_at.isoformat(),
                        'is_read': email.is_read,
                        'is_processed': email.is_processed,
                        'has_attachments': email.has_attachments,
                        'attachment_count': email.attachment_count,
                        'attachments': self._get_email_attachments(session, email.id),
                        # 解析相關欄位
                        'vendor_code': email.vendor_code,
                        'pd': email.pd,
                        'lot': email.lot,
                        'yield_value': email.yield_value,
                        'parse_status': email.parse_status,
                        'parsed_at': email.parsed_at.isoformat() if email.parsed_at else None,
                        'parse_error': email.parse_error,
                        'extraction_method': email.extraction_method
                    }
                    result.append(email_dict)
                
                return result
                
        except Exception as e:
            self.logger.error(f"查詢郵件失敗: {e}")
            return []
    
    def get_email_by_id(self, email_id: int) -> Optional[Dict[str, Any]]:
        """
        根據 ID 取得單一郵件
        
        Args:
            email_id: 郵件 ID
            
        Returns:
            郵件詳細資訊
        """
        try:
            with self.get_session() as session:
                email = session.query(EmailDB).filter_by(id=email_id).first()
                
                if not email:
                    return None
                
                # 取得附件
                attachments = self._get_email_attachments(session, email_id)
                
                # 取得處理狀態
                process_status = self._get_email_process_status(session, email_id)
                
                return {
                    'id': email.id,
                    'message_id': email.message_id,
                    'sender': self.decode_sender(email.sender),
                    'subject': self.decode_mime_header(email.subject),
                    'body': email.body,
                    'received_time': email.received_time.isoformat(),
                    'created_at': email.created_at.isoformat(),
                    'is_read': email.is_read,
                    'is_processed': email.is_processed,
                    'has_attachments': email.has_attachments,
                    'attachment_count': email.attachment_count,
                    'attachments': attachments,
                    'process_status': process_status,
                    # 解析相關欄位
                    'vendor_code': email.vendor_code,
                    'pd': email.pd,
                    'lot': email.lot,
                    'yield_value': email.yield_value,
                    'parse_status': email.parse_status,
                    'parsed_at': email.parsed_at.isoformat() if email.parsed_at else None,
                    'parse_error': email.parse_error,
                    'extraction_method': email.extraction_method
                }
                
        except Exception as e:
            self.logger.error(f"取得郵件失敗: {e}")
            return None
    
    def get_senders(self) -> List[Dict[str, Any]]:
        """
        取得所有寄件者列表
        
        Returns:
            寄件者列表
        """
        try:
            with self.get_session() as session:
                senders = session.query(SenderDB).order_by(desc(SenderDB.total_emails)).all()
                
                result = []
                for sender in senders:
                    sender_dict = {
                        'id': sender.id,
                        'email_address': sender.email_address,
                        'display_name': sender.display_name,
                        'total_emails': sender.total_emails,
                        'last_email_time': sender.last_email_time.isoformat() if sender.last_email_time else None,
                        'first_email_time': sender.first_email_time.isoformat() if sender.first_email_time else None
                    }
                    result.append(sender_dict)
                
                return result
                
        except Exception as e:
            self.logger.error(f"取得寄件者列表失敗: {e}")
            return []
    
    def get_email_count(self, sender: Optional[str] = None) -> int:
        """
        取得郵件總數
        
        Args:
            sender: 寄件者篩選
            
        Returns:
            郵件數量
        """
        try:
            with self.get_session() as session:
                query = session.query(func.count(EmailDB.id))
                
                if sender:
                    query = query.filter(EmailDB.sender == sender)
                
                return query.scalar() or 0
                
        except Exception as e:
            self.logger.error(f"計算郵件數量失敗: {e}")
            return 0
    
    def mark_email_as_read(self, email_id: int) -> bool:
        """
        標記郵件為已讀
        
        Args:
            email_id: 郵件 ID
            
        Returns:
            操作成功與否
        """
        try:
            with self.get_session() as session:
                email = session.query(EmailDB).filter_by(id=email_id).first()
                
                if email:
                    email.is_read = True
                    session.commit()
                    self.logger.debug(f"郵件已標記為已讀: {email_id}")
                    return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"標記郵件已讀失敗: {e}")
            return False
    
    def delete_email(self, email_id: int) -> bool:
        """
        刪[EXCEPT_CHAR]郵件
        
        Args:
            email_id: 郵件 ID
            
        Returns:
            操作成功與否
        """
        try:
            with self.get_session() as session:
                email = session.query(EmailDB).filter_by(id=email_id).first()
                
                if email:
                    # 更新寄件者統計
                    self._decrease_sender_stats(session, email.sender)
                    
                    # 刪[EXCEPT_CHAR]郵件（附件和處理狀態會因為 cascade 自動刪[EXCEPT_CHAR]）
                    session.delete(email)
                    session.commit()
                    
                    self.logger.info(f"郵件已刪[EXCEPT_CHAR]: {email_id}")
                    return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"刪[EXCEPT_CHAR]郵件失敗: {e}")
            return False
    
    def search_emails(self, 
                     search_term: str,
                     search_fields: List[str] = None,
                     sender: Optional[str] = None,
                     limit: int = 50) -> List[Dict[str, Any]]:
        """
        搜尋郵件
        
        Args:
            search_term: 搜尋關鍵字
            search_fields: 搜尋欄位列表
            sender: 寄件者篩選
            limit: 限制數量
            
        Returns:
            搜尋結果
        """
        try:
            if not search_fields:
                search_fields = ['subject', 'body', 'sender']
            
            with self.get_session() as session:
                # 建立搜尋條件
                conditions = []
                
                if 'subject' in search_fields:
                    conditions.append(EmailDB.subject.contains(search_term))
                if 'body' in search_fields:
                    conditions.append(EmailDB.body.contains(search_term))
                if 'sender' in search_fields:
                    conditions.append(EmailDB.sender.contains(search_term))
                
                query = session.query(EmailDB).filter(or_(*conditions))
                
                # 寄件者篩選
                if sender:
                    query = query.filter(EmailDB.sender == sender)
                
                # 排序和限制
                emails = query.order_by(desc(EmailDB.received_time)).limit(limit).all()
                
                # 轉換結果
                result = []
                for email in emails:
                    email_dict = {
                        'id': email.id,
                        'message_id': email.message_id,
                        'sender': email.sender,
                        'subject': email.subject,
                        'body': email.body[:200] + "..." if len(email.body) > 200 else email.body,
                        'received_time': email.received_time.isoformat(),
                        'has_attachments': email.has_attachments,
                        'attachment_count': email.attachment_count
                    }
                    result.append(email_dict)
                
                return result
                
        except Exception as e:
            self.logger.error(f"搜尋郵件失敗: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        取得資料庫統計資訊
        
        Returns:
            統計資訊
        """
        try:
            with self.get_session() as session:
                total_emails = session.query(func.count(EmailDB.id)).scalar() or 0
                total_senders = session.query(func.count(SenderDB.id)).scalar() or 0
                unread_emails = session.query(func.count(EmailDB.id)).filter_by(is_read=False).scalar() or 0
                processed_emails = session.query(func.count(EmailDB.id)).filter_by(is_processed=True).scalar() or 0
                emails_with_attachments = session.query(func.count(EmailDB.id)).filter_by(has_attachments=True).scalar() or 0
                
                # 最新郵件時間
                latest_email = session.query(EmailDB.received_time).order_by(desc(EmailDB.received_time)).first()
                latest_email_time = latest_email[0].isoformat() if latest_email else None
                
                return {
                    'total_emails': total_emails,
                    'total_senders': total_senders,
                    'unread_emails': unread_emails,
                    'processed_emails': processed_emails,
                    'emails_with_attachments': emails_with_attachments,
                    'latest_email_time': latest_email_time
                }
                
        except Exception as e:
            self.logger.error(f"取得統計資訊失敗: {e}")
            return {}
    
    def _get_email_attachments(self, session: Session, email_id: int) -> List[Dict[str, Any]]:
        """取得郵件附件列表"""
        attachments = session.query(AttachmentDB).filter_by(email_id=email_id).all()
        
        result = []
        for attachment in attachments:
            attachment_dict = {
                'id': attachment.id,
                'filename': attachment.filename,
                'content_type': attachment.content_type,
                'size_bytes': attachment.size_bytes,
                'file_path': attachment.file_path,
                'is_processed': attachment.is_processed
            }
            result.append(attachment_dict)
        
        return result
    
    def _get_email_process_status(self, session: Session, email_id: int) -> List[Dict[str, Any]]:
        """取得郵件處理狀態"""
        statuses = session.query(EmailProcessStatusDB).filter_by(email_id=email_id).all()
        
        result = []
        for status in statuses:
            status_dict = {
                'id': status.id,
                'step_name': status.step_name,
                'status': status.status,
                'started_at': status.started_at.isoformat() if status.started_at else None,
                'completed_at': status.completed_at.isoformat() if status.completed_at else None,
                'error_message': status.error_message,
                'progress_percentage': status.progress_percentage
            }
            result.append(status_dict)
        
        return result
    
    def _update_sender_stats(self, session: Session, sender_email: str, email_time: datetime, display_name: Optional[str] = None):
        """更新寄件者統計"""
        sender = session.query(SenderDB).filter_by(email_address=sender_email).first()
        
        if sender:
            sender.total_emails += 1
            sender.last_email_time = max(sender.last_email_time or email_time, email_time)
            sender.first_email_time = min(sender.first_email_time or email_time, email_time)
            sender.updated_at = datetime.utcnow()
            
            # 更新顯示名稱（如果提供且當前為空）
            if display_name and not sender.display_name:
                sender.display_name = display_name
        else:
            sender = SenderDB(
                email_address=sender_email,
                display_name=display_name,
                total_emails=1,
                last_email_time=email_time,
                first_email_time=email_time
            )
            session.add(sender)
    
    def _decrease_sender_stats(self, session: Session, sender_email: str):
        """減少寄件者統計"""
        sender = session.query(SenderDB).filter_by(email_address=sender_email).first()
        
        if sender and sender.total_emails > 0:
            sender.total_emails -= 1
            sender.updated_at = datetime.utcnow()
            
            # 如果沒有郵件了，可以選擇刪[EXCEPT_CHAR]寄件者記錄
            if sender.total_emails == 0:
                session.delete(sender)
    
    def fix_null_display_names(self) -> Dict[str, Any]:
        """
        修復現有的 NULL 顯示名稱
        嘗試從寄件者郵件地址中提取顯示名稱
        
        Returns:
            修復結果統計
        """
        try:
            with self.get_session() as session:
                # 查找所有 display_name 為 NULL 的寄件者
                null_senders = session.query(SenderDB).filter(
                    SenderDB.display_name.is_(None)
                ).all()
                
                if not null_senders:
                    return {
                        'success': True,
                        'message': '沒有需要修復的寄件者記錄',
                        'data': {
                            'total_senders': 0,
                            'fixed_count': 0
                        }
                    }
                
                fixed_count = 0
                
                # 嘗試從對應的 emails 表中獲取 sender_display_name
                for sender in null_senders:
                    try:
                        # 查找該寄件者的最新郵件，看是否有顯示名稱
                        email_with_display_name = session.query(EmailDB).filter(
                            EmailDB.sender == sender.email_address,
                            EmailDB.sender_display_name.is_not(None)
                        ).order_by(desc(EmailDB.received_time)).first()
                        
                        if email_with_display_name and email_with_display_name.sender_display_name:
                            sender.display_name = email_with_display_name.sender_display_name
                            sender.updated_at = datetime.utcnow()
                            fixed_count += 1
                            self.logger.debug(f"修復寄件者顯示名稱: {sender.email_address} -> {sender.display_name}")
                        else:
                            # 如果沒有找到顯示名稱，嘗試從郵件地址中提取
                            if '@' in sender.email_address:
                                local_part = sender.email_address.split('@')[0]
                                # 簡單清理：移除數字、點號、下劃線等
                                cleaned_name = local_part.replace('.', ' ').replace('_', ' ')
                                if cleaned_name != sender.email_address:
                                    sender.display_name = cleaned_name.title()
                                    sender.updated_at = datetime.utcnow()
                                    fixed_count += 1
                                    self.logger.debug(f"從郵件地址推斷顯示名稱: {sender.email_address} -> {sender.display_name}")
                    except Exception as e:
                        self.logger.warning(f"修復寄件者 {sender.email_address} 的顯示名稱失敗: {e}")
                        continue
                
                session.commit()
                
                self.logger.info(f"成功修復 {fixed_count}/{len(null_senders)} 個寄件者的顯示名稱")
                
                return {
                    'success': True,
                    'message': f'成功修復 {fixed_count} 個寄件者的顯示名稱',
                    'data': {
                        'total_senders': len(null_senders),
                        'fixed_count': fixed_count
                    }
                }
                
        except Exception as e:
            self.logger.error(f"修復顯示名稱失敗: {e}")
            return {
                'success': False,
                'message': f'修復顯示名稱失敗: {str(e)}',
                'data': {
                    'error': str(e)
                }
            }
    
    def fix_old_emails_display_names(self) -> Dict[str, Any]:
        """
        修復舊郵件的 sender_display_name 欄位
        從 sender 欄位解析顯示名稱
        
        Returns:
            修復結果統計
        """
        try:
            with self.get_session() as session:
                # 查找所有 sender_display_name 為 NULL 的舊郵件
                null_emails = session.query(EmailDB).filter(
                    EmailDB.sender_display_name.is_(None)
                ).all()
                
                if not null_emails:
                    return {
                        'success': True,
                        'message': '沒有需要修復的郵件記錄',
                        'data': {
                            'total_emails': 0,
                            'fixed_count': 0
                        }
                    }
                
                fixed_count = 0
                
                # 處理每封郵件
                for email in null_emails:
                    try:
                        # 嘗試從 sender 欄位解析顯示名稱
                        sender_display_name = self._extract_display_name_from_sender(email.sender)
                        
                        if sender_display_name:
                            email.sender_display_name = sender_display_name
                            fixed_count += 1
                            self.logger.debug(f"修復郵件顯示名稱: {email.id} -> {sender_display_name}")
                            
                            # 同時更新對應的 senders 表
                            sender_record = session.query(SenderDB).filter_by(
                                email_address=email.sender
                            ).first()
                            
                            if sender_record and not sender_record.display_name:
                                sender_record.display_name = sender_display_name
                                sender_record.updated_at = datetime.utcnow()
                                
                    except Exception as e:
                        self.logger.warning(f"修復郵件 {email.id} 的顯示名稱失敗: {e}")
                        continue
                
                session.commit()
                
                self.logger.info(f"成功修復 {fixed_count}/{len(null_emails)} 封郵件的顯示名稱")
                
                return {
                    'success': True,
                    'message': f'成功修復 {fixed_count} 封郵件的顯示名稱',
                    'data': {
                        'total_emails': len(null_emails),
                        'fixed_count': fixed_count
                    }
                }
                
        except Exception as e:
            self.logger.error(f"修復郵件顯示名稱失敗: {e}")
            return {
                'success': False,
                'message': f'修復郵件顯示名稱失敗: {str(e)}',
                'data': {
                    'error': str(e)
                }
            }
    
    def _extract_display_name_from_sender(self, sender: str) -> Optional[str]:
        """
        從 sender 欄位提取顯示名稱
        支援多種格式：
        - "Display Name <<EMAIL>>"
        - "<EMAIL>"
        - "Display Name"
        
        Args:
            sender: 寄件者字串
            
        Returns:
            提取的顯示名稱，如果無法提取則返回 None
        """
        try:
            if not sender:
                return None
            
            # 使用 parseaddr 解析郵件地址格式
            from email.utils import parseaddr
            name, email_addr = parseaddr(sender)
            
            # 如果有解析到名稱，優先使用
            if name and name.strip():
                # 解碼可能的 MIME 編碼
                decoded_name = self.decode_mime_header(name.strip())
                return decoded_name
            
            # 如果沒有名稱，嘗試從郵件地址推斷
            if email_addr and '@' in email_addr:
                local_part = email_addr.split('@')[0]
                # 簡單清理：移除數字、點號、下劃線等
                cleaned_name = local_part.replace('.', ' ').replace('_', ' ')
                if cleaned_name != email_addr and len(cleaned_name) > 2:
                    return cleaned_name.title()
            
            return None
            
        except Exception as e:
            self.logger.warning(f"從 sender 提取顯示名稱失敗: {sender}, 錯誤: {e}")
            return None
    
    def get_attachment_by_id(self, attachment_id: int) -> Optional[AttachmentDB]:
        """
        根據附件 ID 取得附件資訊
        
        Args:
            attachment_id: 附件 ID
            
        Returns:
            附件資訊，如果不存在則返回 None
        """
        try:
            with self.get_session() as session:
                attachment = session.query(AttachmentDB).filter_by(id=attachment_id).first()
                if attachment:
                    # 確保在 session 外仍可訪問屬性
                    session.expunge(attachment)
                return attachment
        except Exception as e:
            self.logger.error(f"取得附件資訊失敗: {e}")
            return None
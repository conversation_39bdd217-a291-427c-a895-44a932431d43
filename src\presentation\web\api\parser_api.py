"""
解析相關 API
提供郵件解析的 RESTful API
"""

from flask import Blueprint, jsonify, request
from datetime import datetime

from src.infrastructure.parsers.base_parser import ParserFactory, ParserRegistry
from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.adapters.database.models import EmailDB
from src.data_models.email_models import EmailData, VendorIdentificationResult, EmailParsingResult
from src.infrastructure.logging.logger_manager import LoggerManager


parser_bp = Blueprint('parser', __name__, url_prefix='/api/parser')
logger = LoggerManager().get_logger("ParserAPI")

# 使用現有的 ParserFactory 而不是新建服務
parser_factory = ParserFactory()
database = EmailDatabase()

# 註冊現有的解析器
def _register_parsers():
    """註冊所有現有的解析器"""
    try:
        from src.infrastructure.parsers.gtk_parser import GTKParser
        from src.infrastructure.parsers.etd_parser import ETDParser
        from src.infrastructure.parsers.jcet_parser import JCETParser
        from src.infrastructure.parsers.lingsen_parser import LINGSENParser
        from src.infrastructure.parsers.xaht_parser import XAHTParser
        
        registry = ParserRegistry()
        
        # 檢查並註冊解析器（支援安全重複註冊）
        parsers_to_register = [
            ("GTK", GTKParser()),
            ("ETD", ETDParser()),
            ("JCET", JCETParser()),
            ("LINGSEN", LINGSENParser()),
            ("XAHT", XAHTParser())
        ]
        
        registered_count = 0
        for vendor_code, parser in parsers_to_register:
            if not registry.is_registered(vendor_code):
                registry.register_parser(vendor_code, parser)
                registered_count += 1
                logger.debug(f"已註冊 {vendor_code} 解析器")
            else:
                logger.debug(f"{vendor_code} 解析器已存在，跳過註冊")
        
        if registered_count > 0:
            logger.info(f"API 模組已註冊 {registered_count} 個新解析器")
        else:
            logger.debug("API 模組：所有解析器都已註冊，無需重複註冊")
            
    except Exception as e:
        logger.error(f"API 模組註冊解析器失敗: {e}")

# 初始化時註冊解析器
_register_parsers()


@parser_bp.route('/emails/<int:email_id>/reparse', methods=['POST'])
def reparse_email(email_id: int):
    """重新解析郵件"""
    try:
        # 取得郵件資料
        email = database.get_email_by_id(email_id)
        if not email:
            return jsonify({
                'success': False, 
                'error': '郵件不存在'
            }), 404
            
        # 轉換為 EmailData
        email_data = EmailData(
            message_id=email['message_id'],
            subject=email['subject'],
            sender=email['sender'],
            body=email.get('body', ''),
            received_time=datetime.fromisoformat(email['received_time'])
        )
        
        # 重新解析
        result = parser_factory.parse_email(email_data)
        if isinstance(result, tuple) and len(result) == 2:
            vendor_result, parsing_result = result
        else:
            # 如果返回的不是 tuple，可能是解析失敗
            vendor_result = VendorIdentificationResult(
                vendor_code=None,
                vendor_name=None,
                confidence_score=0.0,
                matching_patterns=[],
                is_identified=False
            )
            parsing_result = result if result else EmailParsingResult(
                is_success=False,
                error_message="解析器返回無效結果",
                validation_errors=["解析器返回格式錯誤"]
            )
        
        # 轉換結果格式
        result = {
            'success': parsing_result.is_success,
            'vendor': vendor_result.vendor_code,
            'vendor_name': vendor_result.vendor_name,
            'pd': parsing_result.extracted_data.get('product') or parsing_result.extracted_data.get('product_name'),
            'lot': parsing_result.lot_number,
            'yield': parsing_result.extracted_data.get('yield_value'),
            'confidence': vendor_result.confidence_score,
            'error': parsing_result.error_message,
            'extraction_method': parsing_result.extraction_method
        }
        
        # 更新資料庫
        if result['success']:
            from src.infrastructure.adapters.database.models import EmailDB
            
            with database.get_session() as session:
                email_db = session.query(EmailDB).filter_by(id=email_id).first()
                if email_db:
                    email_db.vendor_code = result.get('vendor')
                    email_db.pd = result.get('pd')
                    email_db.lot = result.get('lot')
                    email_db.yield_value = result.get('yield')
                    email_db.extraction_method = result.get('extraction_method')
                    email_db.parsed_at = datetime.now()
                    email_db.parse_status = 'parsed'
                    email_db.parse_error = None
                    session.commit()
                    
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"重新解析郵件失敗: {e}")
        return jsonify({
            'success': False, 
            'error': str(e)
        }), 500


@parser_bp.route('/emails/batch-parse', methods=['POST'])
def batch_parse_emails():
    """批次解析郵件"""
    try:
        # 取得請求參數
        data = request.get_json() or {}
        limit = data.get('limit', 100)
        parse_failed_only = data.get('failed_only', False)
        
        # 查詢待解析郵件
        from src.infrastructure.adapters.database.models import EmailDB
        
        with database.get_session() as session:
            query = session.query(EmailDB)
            
            if parse_failed_only:
                query = query.filter_by(parse_status='failed')
            else:
                query = query.filter_by(parse_status='pending')
                
            emails = query.limit(limit).all()
            
            if not emails:
                return jsonify({
                    'success': True,
                    'message': '沒有待解析的郵件',
                    'parsed_count': 0
                })
                
            # 批次解析
            results = []
            for email_db in emails:
                try:
                    email_data = EmailData(
                        message_id=email_db.message_id,
                        subject=email_db.subject,
                        sender=email_db.sender,
                        body=email_db.body or '',
                        received_time=email_db.received_time
                    )
                    
                    result = parser_factory.parse_email(email_data)
                    if isinstance(result, tuple) and len(result) == 2:
                        vendor_result, parsing_result = result
                    else:
                        vendor_result = VendorIdentificationResult(
                            vendor_code=None,
                            vendor_name=None,
                            confidence_score=0.0,
                            matching_patterns=[],
                            is_identified=False
                        )
                        parsing_result = result if result else EmailParsingResult(
                            is_success=False,
                            error_message="解析器返回無效結果",
                            validation_errors=["解析器返回格式錯誤"]
                        )
                    
                    # 轉換結果格式
                    result = {
                        'success': parsing_result.is_success,
                        'vendor': vendor_result.vendor_code,
                        'vendor_name': vendor_result.vendor_name,
                        'pd': parsing_result.extracted_data.get('product') or parsing_result.extracted_data.get('product_name'),
                        'lot': parsing_result.lot_number,
                        'yield': parsing_result.extracted_data.get('yield_value'),
                        'confidence': vendor_result.confidence_score,
                        'error': parsing_result.error_message,
                        'extraction_method': parsing_result.extraction_method,
                        'email_id': email_db.id
                    }
                    
                    # 更新資料庫
                    if result['success']:
                        email_db.vendor_code = result.get('vendor')
                        email_db.pd = result.get('pd')
                        email_db.lot = result.get('lot')
                        email_db.yield_value = result.get('yield')
                        email_db.extraction_method = result.get('extraction_method')
                        email_db.parsed_at = datetime.now()
                        email_db.parse_status = 'parsed'
                        email_db.parse_error = None
                    else:
                        email_db.parse_status = 'failed'
                        email_db.parse_error = result.get('error')
                        
                    results.append(result)
                    
                except Exception as e:
                    logger.error(f"批次解析郵件 {email_db.id} 失敗: {e}")
                    results.append({
                        'success': False,
                        'email_id': email_db.id,
                        'subject': email_db.subject,
                        'error': str(e)
                    })
                    
            # 提交所有更新
            session.commit()
            
        success_count = sum(1 for r in results if r['success'])
        
        return jsonify({
            'success': True,
            'parsed_count': len(results),
            'success_count': success_count,
            'failed_count': len(results) - success_count,
            'results': results
        })
        
    except Exception as e:
        logger.error(f"批次解析失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@parser_bp.route('/statistics', methods=['GET'])
def get_parser_statistics():
    """取得解析統計"""
    try:
        # 使用 ParserFactory 取得基本資訊
        supported_vendors = parser_factory.get_supported_vendors()
        
        stats = {
            'supported_vendors': supported_vendors,
            'registered_parsers': len(supported_vendors)
        }
        
        # 添加資料庫統計
        from src.infrastructure.adapters.database.models import EmailDB
        
        with database.get_session() as session:
            total_emails = session.query(EmailDB).count()
            parsed_emails = session.query(EmailDB).filter_by(parse_status='parsed').count()
            failed_emails = session.query(EmailDB).filter_by(parse_status='failed').count()
            pending_emails = session.query(EmailDB).filter_by(parse_status='pending').count()
            
            # 廠商分布
            vendor_stats = session.query(
                EmailDB.vendor_code, 
                session.query(EmailDB).filter(EmailDB.vendor_code.isnot(None)).count()
            ).group_by(EmailDB.vendor_code).all()
            
        stats['database_stats'] = {
            'total_emails': total_emails,
            'parsed_emails': parsed_emails,
            'failed_emails': failed_emails,
            'pending_emails': pending_emails,
            'vendor_distribution': dict(vendor_stats) if vendor_stats else {}
        }
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"取得統計失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@parser_bp.route('/test', methods=['POST'])
def test_parse_email():
    """測試解析（用於[EXCEPT_CHAR]錯）"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少請求資料'
            }), 400
            
        # 創建測試郵件資料
        email_data = EmailData(
            message_id=data.get('message_id', f'test-{datetime.now().timestamp()}'),
            subject=data.get('subject', ''),
            sender=data.get('sender', '<EMAIL>'),
            body=data.get('body', ''),
            received_time=datetime.now()
        )
        
        # 測試解析
        result = parser_factory.parse_email(email_data)
        if isinstance(result, tuple) and len(result) == 2:
            vendor_result, parsing_result = result
        else:
            vendor_result = VendorIdentificationResult(
                vendor_code=None,
                vendor_name=None,
                confidence_score=0.0,
                matching_patterns=[],
                is_identified=False
            )
            parsing_result = result if result else EmailParsingResult(
                is_success=False,
                error_message="解析器返回無效結果",
                validation_errors=["解析器返回格式錯誤"]
            )
        
        result = {
            'vendor_identification': {
                'is_identified': vendor_result.is_identified,
                'vendor_code': vendor_result.vendor_code,
                'vendor_name': vendor_result.vendor_name,
                'confidence_score': vendor_result.confidence_score,
                'matching_patterns': vendor_result.matching_patterns
            },
            'parse_result': {
                'success': parsing_result.is_success,
                'vendor': vendor_result.vendor_code,
                'pd': parsing_result.extracted_data.get('product') or parsing_result.extracted_data.get('product_name'),
                'lot': parsing_result.lot_number,
                'yield': parsing_result.extracted_data.get('yield_value'),
                'error': parsing_result.error_message
            },
            'email_info': {
                'subject': email_data.subject,
                'sender': email_data.sender,
                'has_body': bool(email_data.body),
                'attachment_count': len(email_data.attachments)
            }
        }
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"測試解析失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@parser_bp.route('/emails/batch-process', methods=['POST'])
def batch_process_files():
    """批次處理已解析郵件的檔案"""
    try:
        # 查詢已解析但未處理檔案的郵件
        from src.infrastructure.adapters.database.models import EmailDB
        import os
        import shutil
        from pathlib import Path
        
        with database.get_session() as session:
            # 查詢已解析且有解析結果的郵件
            emails = session.query(EmailDB).filter(
                EmailDB.parse_status == 'parsed',
                EmailDB.vendor_code.isnot(None),
                EmailDB.pd.isnot(None),
                EmailDB.lot.isnot(None)
            ).all()
            
            if not emails:
                return jsonify({
                    'success': True,
                    'message': '沒有已解析的郵件需要處理',
                    'processed_count': 0,
                    'success_count': 0,
                    'failed_count': 0
                })
            
            # 取得設定
            temp_base_path = os.getenv('FILE_TEMP_BASE_PATH', 'D:\\temp')
            source_base_path = os.getenv('FILE_SOURCE_BASE_PATH', '\\\\192.168.1.60\\test_log')
            
            processed_count = 0
            success_count = 0
            failed_count = 0
            
            for email in emails:
                try:
                    # 檢查是否已經處理過
                    if email.is_processed:
                        logger.debug(f"郵件 {email.id} 已處理過，跳過")
                        continue
                    
                    processed_count += 1
                    vendor_code = email.vendor_code
                    pd = email.pd or 'default'
                    lot = email.lot or 'default'
                    
                    # 建立目標路徑 D:\temp\{pd}\{lot}
                    target_path = Path(temp_base_path) / pd / lot
                    target_path.mkdir(parents=True, exist_ok=True)
                    
                    # 檢查是否有廠商檔案處理路徑（暫時跳過，直接處理附件）
                    logger.info(f"開始處理郵件 {email.id}: {vendor_code} -> {target_path}")
                    
                    # 直接處理附件（簡化流程）
                    files_copied = 0
                    
                    # 嘗試直接從來源路徑處理廠商檔案
                    try:
                        from src.infrastructure.adapters.file_handlers import FileHandlerFactory
                        
                        factory = FileHandlerFactory(source_base_path)
                        mo = lot  # 使用 lot 作為 mo
                        
                        result = factory.process_vendor_files(
                            vendor_code=vendor_code,
                            mo=mo,
                            temp_path=str(target_path),
                            pd=pd,
                            lot=lot
                        )
                        
                        if result['success']:
                            logger.info(f"成功處理廠商檔案 {email.id}: {vendor_code} -> {target_path}")
                            files_copied += 1
                        else:
                            logger.warning(f"處理廠商檔案失敗 {email.id}: {result.get('error')}")
                    except Exception as e:
                        logger.warning(f"無法處理廠商檔案 {email.id}: {e}")
                    
                    # 處理附件
                    from src.infrastructure.adapters.database.models import AttachmentDB
                    attachments = session.query(AttachmentDB).filter_by(email_id=email.id).all()
                    
                    if attachments:
                        logger.info(f"處理郵件 {email.id} 的 {len(attachments)} 個附件")
                        for attachment in attachments:
                            try:
                                if attachment.file_path and Path(attachment.file_path).exists():
                                    # 複製附件到目標路徑
                                    source_file = Path(attachment.file_path)
                                    target_file = target_path / attachment.filename
                                    shutil.copy2(source_file, target_file)
                                    logger.debug(f"複製附件: {attachment.filename} -> {target_file}")
                                    files_copied += 1
                                else:
                                    logger.warning(f"附件檔案不存在: {attachment.filename} ({attachment.file_path})")
                            except Exception as e:
                                logger.error(f"複製附件 {attachment.filename} 失敗: {e}")
                    
                    # 更新郵件處理狀態
                    if files_copied > 0:
                        email.is_processed = True
                        logger.info(f"成功處理郵件 {email.id}，複製了 {files_copied} 個檔案到 {target_path}")
                        success_count += 1
                    else:
                        logger.warning(f"郵件 {email.id} 沒有可處理的檔案")
                        failed_count += 1
                    
                except Exception as e:
                    logger.error(f"處理郵件 {email.id} 時發生錯誤: {e}")
                    failed_count += 1
            
            # 提交資料庫變更
            session.commit()
            
            return jsonify({
                'success': True,
                'message': f'批次處理完成',
                'processed_count': processed_count,
                'success_count': success_count,
                'failed_count': failed_count
            })
        
    except Exception as e:
        logger.error(f"批次處理檔案失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@parser_bp.route('/emails/<int:email_id>/llm-analysis', methods=['GET'])
def get_llm_analysis(email_id):
    """獲取郵件的 LLM 分析結果"""
    try:
        logger.info(f"請求 LLM 分析結果: email_id={email_id}")
        
        # 獲取郵件資料
        with database.get_session() as session:
            email = session.query(EmailDB).filter(EmailDB.id == email_id).first()
            
            if not email:
                return jsonify({
                    'success': False,
                    'error': '郵件不存在'
                }), 404
            
            # 檢查是否為 LLM 解析
            if email.extraction_method != 'llm':
                return jsonify({
                    'success': False,
                    'error': '此郵件非 LLM 解析'
                }), 400
            
            # 在會話內獲取所有需要的資料
            email_data = {
                'email_id': email_id,
                'subject': email.subject,
                'sender': email.sender,
                'body': email.body,
                'received_time': email.received_time.isoformat() if email.received_time else None,
                'extraction_method': email.extraction_method,
                'parse_status': email.parse_status,
                'parsed_at': email.parsed_at.isoformat() if email.parsed_at else None,
                'database_results': {
                    'vendor_code': email.vendor_code,
                    'pd': email.pd,
                    'lot': email.lot,
                    'yield_value': email.yield_value,
                    'mo_number': None  # EmailDB 模型沒有 mo_number 欄位
                }
            }
        
        # 暫時使用模擬的 LLM 結果進行測試
        class MockLLMResult:
            def __init__(self):
                self.vendor_code = "JCET"
                self.product_code = "G2892K21D(CA)"
                self.lot_number = "YHW0049.Y"
                self.mo_number = "TS25063596.1"
                self.yield_rate = 97.38435
                self.test_batch = "TS25063596.1"
                self.product_description = "台湾致新产品"
                self.confidence_score = 0.85
                self.raw_response = '{"vendor_code":"JCET","product_code":"G2892K21D(CA)","lot_number":"YHW0049.Y","yield_rate":97.38435}'
                self.is_success = True
                self.error_message = None
        
        llm_result = MockLLMResult()
        
        # 組織分析結果
        analysis_data = email_data.copy()
        analysis_data['llm_analysis'] = {
            'vendor_code': llm_result.vendor_code,
            'product_code': llm_result.product_code,
            'lot_number': llm_result.lot_number,
            'mo_number': llm_result.mo_number,
            'yield_rate': llm_result.yield_rate,
            'test_batch': llm_result.test_batch,
            'product_description': llm_result.product_description,
            'confidence_score': llm_result.confidence_score,
            'raw_response': llm_result.raw_response,
            'is_success': llm_result.is_success,
            'error_message': llm_result.error_message
        }
        
        return jsonify({
            'success': True,
            'data': analysis_data
        })
        
    except Exception as e:
        logger.error(f"獲取 LLM 分析結果失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
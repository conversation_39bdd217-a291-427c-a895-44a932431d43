"""
郵件 Web API 服務
提供 Flask 路由和 API 端點
"""

from flask import Flask, Blueprint, request, jsonify, render_template
from typing import Dict, Any, List, Optional
from datetime import datetime
import traceback

from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.logging.logger_manager import LoggerManager


class EmailWebService:
    """
    郵件 Web API 服務類
    提供郵件相關的 RESTful API 服務
    """
    
    def __init__(self, database: EmailDatabase = None):
        """
        初始化郵件 Web 服務
        
        Args:
            database: 郵件資料庫實例
        """
        self.logger = LoggerManager().get_logger("EmailWebService")
        self.database = database or EmailDatabase()
        
        self.logger.info("郵件 Web API 服務已初始化")
    
    def get_emails_api(self) -> Dict[str, Any]:
        """
        取得郵件列表 API
        
        Returns:
            API 回應字典
        """
        try:
            # 取得查詢參數
            sender = request.args.get('sender')
            limit = int(request.args.get('limit', 50))
            offset = int(request.args.get('offset', 0))
            order_by = request.args.get('order_by', 'received_time')
            order_desc = request.args.get('order_desc', 'true').lower() == 'true'
            
            # 查詢郵件
            emails = self.database.get_emails(
                sender=sender,
                limit=limit,
                offset=offset,
                order_by=order_by,
                order_desc=order_desc
            )
            
            # 取得總數
            total_count = self.database.get_email_count(sender=sender)
            
            return {
                'success': True,
                'data': {
                    'emails': emails,
                    'total_count': total_count,
                    'has_more': (offset + limit) < total_count
                },
                'message': f'成功取得 {len(emails)} 封郵件'
            }
            
        except Exception as e:
            self.logger.error(f"取得郵件列表失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '取得郵件列表失敗'
            }
    
    def get_email_detail_api(self, email_id: int) -> Dict[str, Any]:
        """
        取得單一郵件詳情 API
        
        Args:
            email_id: 郵件 ID
            
        Returns:
            API 回應字典
        """
        try:
            email = self.database.get_email_by_id(email_id)
            
            if not email:
                return {
                    'success': False,
                    'error': 'Email not found',
                    'message': f'找不到郵件 ID: {email_id}'
                }
            
            # 標記為已讀
            self.database.mark_email_as_read(email_id)
            
            # 調試輸出
            self.logger.info(f"Email data keys: {list(email.keys())}")
            self.logger.info(f"extraction_method in email: {'extraction_method' in email}")
            if 'extraction_method' in email:
                self.logger.info(f"extraction_method value: {email['extraction_method']}")
            
            return {
                'success': True,
                'data': email,
                'message': '成功取得郵件詳情'
            }
            
        except Exception as e:
            self.logger.error(f"取得郵件詳情失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '取得郵件詳情失敗'
            }
    
    def get_email_failed_analysis_api(self, email_id: int) -> Dict[str, Any]:
        """
        取得解析失敗詳情 API
        
        Args:
            email_id: 郵件 ID
            
        Returns:
            API 回應字典
        """
        try:
            email = self.database.get_email_by_id(email_id)
            
            if not email:
                return {
                    'success': False,
                    'error': 'Email not found',
                    'message': f'找不到郵件 ID: {email_id}'
                }
            
            # 檢查是否為解析失敗的郵件
            if email.get('parse_status') != 'failed':
                return {
                    'success': False,
                    'error': 'Email not failed',
                    'message': '此郵件並非解析失敗狀態'
                }
            
            # 獲取詳細的錯誤信息
            error_message = email.get('error_message', '')
            extraction_method = email.get('extraction_method', '')
            
            # 構建解析失敗詳情
            failed_analysis_data = {
                'email_id': email_id,
                'subject': email.get('subject', ''),
                'sender': email.get('sender', ''),
                'received_time': email.get('received_time', ''),
                'parsed_at': email.get('parsed_at', ''),
                'error_message': error_message,
                'detailed_error': self._get_detailed_error_info(error_message),
                'error_type': self._determine_error_type(error_message),
                'extraction_method': extraction_method,
                'parsing_flow': self._get_parsing_flow(email),
                'llm_analysis': None
            }
            
            # 如果有LLM分析嘗試的資料，添加到結果中
            if email.get('extraction_method') == 'llm' or 'llm' in email.get('error_message', '').lower():
                failed_analysis_data['llm_analysis'] = {
                    'raw_response': email.get('body', ''),  # 可能包含LLM嘗試解析的內容
                    'vendor_code': email.get('vendor_code', ''),
                    'pd': email.get('pd', ''),
                    'lot': email.get('lot', ''),
                    'yield_rate': email.get('yield_rate', ''),
                    'error_details': email.get('error_message', '')
                }
            
            return {
                'success': True,
                'data': failed_analysis_data,
                'message': '成功取得解析失敗詳情'
            }
            
        except Exception as e:
            self.logger.error(f"取得解析失敗詳情失敗: {e}")
            self.logger.error(f"錯誤詳情: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e),
                'message': '取得解析失敗詳情失敗'
            }
    
    def _determine_error_type(self, error_message: str) -> str:
        """
        根據錯誤訊息判斷錯誤類型
        
        Args:
            error_message: 錯誤訊息
            
        Returns:
            錯誤類型
        """
        if not error_message:
            return '未知錯誤'
        
        error_msg_lower = error_message.lower()
        
        if 'validation error' in error_msg_lower:
            return '資料驗證錯誤'
        elif 'parsing failed' in error_msg_lower:
            return '解析失敗'
        elif 'jcet' in error_msg_lower:
            return 'JCET 格式錯誤'
        elif 'gtk' in error_msg_lower:
            return 'GTK 格式錯誤'
        elif 'timeout' in error_msg_lower:
            return '處理超時'
        elif 'connection' in error_msg_lower:
            return '連線錯誤'
        elif 'llm' in error_msg_lower:
            return 'LLM 分析錯誤'
        else:
            return '系統錯誤'
    
    def _get_detailed_error_info(self, error_message: str) -> str:
        """
        獲取詳細錯誤信息
        
        Args:
            error_message: 原始錯誤訊息
            
        Returns:
            詳細錯誤信息
        """
        if not error_message:
            return '無錯誤信息記錄'
        
        # 如果是 JCET 驗證錯誤，提供更詳細的說明
        if 'jcet parsing failed' in error_message.lower():
            if 'validation error' in error_message.lower():
                return f"JCET 郵件格式驗證失敗。原始錯誤: {error_message}\n\n可能原因:\n1. 郵件內容不符合 JCET 標準格式\n2. 缺少必要的欄位 (如 MO 編號、良率等)\n3. 數據格式不正確"
        
        # 如果是 LLM 錯誤，提供更詳細的說明
        if 'llm' in error_message.lower():
            return f"LLM 分析失敗。原始錯誤: {error_message}\n\n可能原因:\n1. LLM 服務連線問題\n2. 郵件內容過於複雜或格式特殊\n3. LLM 模型無法識別關鍵信息"
        
        # 其他錯誤直接返回原始信息
        return error_message
    
    def _get_parsing_flow(self, email: dict) -> list:
        """
        獲取解析流程信息
        
        Args:
            email: 郵件數據
            
        Returns:
            解析流程步驟列表
        """
        flow_steps = []
        
        # 步驟1: 郵件接收
        flow_steps.append({
            'step': 1,
            'title': '📧 郵件接收',
            'status': 'completed',
            'description': f"郵件於 {email.get('received_time', 'N/A')} 成功接收",
            'details': f"寄件者: {email.get('sender', 'N/A')}"
        })
        
        # 步驟2: 格式識別
        vendor_code = email.get('vendor_code', '')
        if vendor_code:
            flow_steps.append({
                'step': 2,
                'title': '🏢 廠商識別',
                'status': 'completed',
                'description': f"識別為 {vendor_code} 廠商郵件",
                'details': f"廠商代碼: {vendor_code}"
            })
        else:
            flow_steps.append({
                'step': 2,
                'title': '🏢 廠商識別',
                'status': 'failed',
                'description': "無法識別郵件廠商",
                'details': "郵件格式可能不符合任何已知廠商標準"
            })
        
        # 步驟3: 解析方法選擇
        extraction_method = email.get('extraction_method', '')
        if extraction_method:
            method_name = {'traditional': '傳統解析', 'llm': 'LLM 解析', 'hybrid': '混合解析'}.get(extraction_method, extraction_method)
            flow_steps.append({
                'step': 3,
                'title': '⚙️ 解析方法選擇',
                'status': 'completed',
                'description': f"選擇使用 {method_name}",
                'details': f"解析方法: {extraction_method}"
            })
        else:
            flow_steps.append({
                'step': 3,
                'title': '⚙️ 解析方法選擇',
                'status': 'failed',
                'description': "無法選擇合適的解析方法",
                'details': "可能是因為廠商識別失敗或格式不支援"
            })
        
        # 步驟4: 數據解析
        parse_status = email.get('parse_status', '')
        if parse_status == 'parsed':
            flow_steps.append({
                'step': 4,
                'title': '📊 數據解析',
                'status': 'completed',
                'description': "成功解析郵件數據",
                'details': f"解析時間: {email.get('parsed_at', 'N/A')}"
            })
        else:
            error_message = email.get('error_message', '')
            flow_steps.append({
                'step': 4,
                'title': '📊 數據解析',
                'status': 'failed',
                'description': "數據解析失敗",
                'details': f"錯誤: {error_message}" if error_message else "無具體錯誤信息"
            })
        
        # 步驟5: 數據驗證
        if parse_status == 'parsed':
            flow_steps.append({
                'step': 5,
                'title': '✅ 數據驗證',
                'status': 'completed',
                'description': "數據驗證通過",
                'details': "所有必要欄位都已正確解析"
            })
        else:
            flow_steps.append({
                'step': 5,
                'title': '✅ 數據驗證',
                'status': 'failed',
                'description': "數據驗證失敗",
                'details': "解析出的數據不符合驗證規則"
            })
        
        return flow_steps
    
    def get_senders_api(self) -> Dict[str, Any]:
        """
        取得寄件者列表 API
        
        Returns:
            API 回應字典
        """
        try:
            senders = self.database.get_senders()
            
            return {
                'success': True,
                'data': {
                    'senders': senders,
                    'total_count': len(senders)
                },
                'message': f'成功取得 {len(senders)} 位寄件者'
            }
            
        except Exception as e:
            self.logger.error(f"取得寄件者列表失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '取得寄件者列表失敗'
            }
    
    def search_emails_api(self) -> Dict[str, Any]:
        """
        搜尋郵件 API
        
        Returns:
            API 回應字典
        """
        try:
            # 取得搜尋參數
            search_term = request.args.get('q', '').strip()
            search_fields = request.args.get('fields', 'subject,body,sender').split(',')
            sender = request.args.get('sender')
            limit = int(request.args.get('limit', 50))
            
            if not search_term:
                return {
                    'success': False,
                    'error': 'Missing search term',
                    'message': '請提供搜尋關鍵字'
                }
            
            # 執行搜尋
            results = self.database.search_emails(
                search_term=search_term,
                search_fields=search_fields,
                sender=sender,
                limit=limit
            )
            
            return {
                'success': True,
                'data': {
                    'emails': results,
                    'search_term': search_term,
                    'total_count': len(results)
                },
                'message': f'找到 {len(results)} 封相關郵件'
            }
            
        except Exception as e:
            self.logger.error(f"搜尋郵件失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '搜尋郵件失敗'
            }
    
    def delete_email_api(self, email_id: int) -> Dict[str, Any]:
        """
        刪[EXCEPT_CHAR]郵件 API
        
        Args:
            email_id: 郵件 ID
            
        Returns:
            API 回應字典
        """
        try:
            success = self.database.delete_email(email_id)
            
            if success:
                return {
                    'success': True,
                    'message': f'成功刪[EXCEPT_CHAR]郵件 ID: {email_id}'
                }
            else:
                return {
                    'success': False,
                    'error': 'Email not found',
                    'message': f'找不到郵件 ID: {email_id}'
                }
                
        except Exception as e:
            self.logger.error(f"刪[EXCEPT_CHAR]郵件失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '刪[EXCEPT_CHAR]郵件失敗'
            }
    
    def get_statistics_api(self) -> Dict[str, Any]:
        """
        取得統計資訊 API
        
        Returns:
            API 回應字典
        """
        try:
            stats = self.database.get_statistics()
            
            return {
                'success': True,
                'data': stats,
                'message': '成功取得統計資訊'
            }
            
        except Exception as e:
            self.logger.error(f"取得統計資訊失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '取得統計資訊失敗'
            }
    
    def sync_emails_api(self) -> Dict[str, Any]:
        """
        同步郵件 API
        
        Returns:
            API 回應字典
        """
        try:
            # 這裡會調用郵件同步服務
            # 暫時返回成功狀態，實際實作會在郵件同步服務中完成
            return {
                'success': True,
                'message': '郵件同步請求已提交',
                'data': {
                    'sync_status': 'requested',
                    'timestamp': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"同步郵件失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '同步郵件失敗'
            }


def create_email_api_blueprint(database: EmailDatabase = None) -> Blueprint:
    """
    建立郵件 API Blueprint
    
    Args:
        database: 郵件資料庫實例
        
    Returns:
        Flask Blueprint
    """
    api_bp = Blueprint('email_api', __name__, url_prefix='/api')
    email_service = EmailWebService(database)
    
    @api_bp.route('/emails', methods=['GET'])
    def get_emails():
        """取得郵件列表"""
        result = email_service.get_emails_api()
        return jsonify(result), 200 if result['success'] else 400
    
    @api_bp.route('/emails/<int:email_id>', methods=['GET'])
    def get_email_detail(email_id: int):
        """取得郵件詳情"""
        result = email_service.get_email_detail_api(email_id)
        return jsonify(result), 200 if result['success'] else 404
    
    @api_bp.route('/emails/<int:email_id>', methods=['DELETE'])
    def delete_email(email_id: int):
        """刪[EXCEPT_CHAR]郵件"""
        result = email_service.delete_email_api(email_id)
        return jsonify(result), 200 if result['success'] else 404
    
    @api_bp.route('/senders', methods=['GET'])
    def get_senders():
        """取得寄件者列表"""
        result = email_service.get_senders_api()
        return jsonify(result), 200 if result['success'] else 400
    
    @api_bp.route('/search', methods=['GET'])
    def search_emails():
        """搜尋郵件"""
        result = email_service.search_emails_api()
        return jsonify(result), 200 if result['success'] else 400
    
    @api_bp.route('/statistics', methods=['GET'])
    def get_statistics():
        """取得統計資訊"""
        result = email_service.get_statistics_api()
        return jsonify(result), 200 if result['success'] else 400
    
    @api_bp.route('/sync', methods=['POST'])
    def sync_emails():
        """同步郵件"""
        result = email_service.sync_emails_api()
        return jsonify(result), 200 if result['success'] else 400
    
    @api_bp.errorhandler(Exception)
    def handle_api_error(error):
        """API 錯誤處理"""
        logger = LoggerManager().get_logger("EmailAPI")
        logger.error(f"API 錯誤: {error}")
        logger.error(traceback.format_exc())
        
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': '伺服器內部錯誤'
        }), 500
    
    return api_bp


def create_email_web_blueprint(database: EmailDatabase = None) -> Blueprint:
    """
    建立郵件 Web 頁面 Blueprint
    
    Args:
        database: 郵件資料庫實例
        
    Returns:
        Flask Blueprint
    """
    web_bp = Blueprint('email_web', __name__)
    email_service = EmailWebService(database)
    
    @web_bp.route('/')
    def inbox():
        """郵件收件夾主頁面"""
        try:
            # 取得基本統計資訊
            stats = email_service.get_statistics_api()
            senders = email_service.get_senders_api()
            
            return render_template('email_inbox.html', 
                                 statistics=stats.get('data', {}),
                                 senders=senders.get('data', {}).get('senders', []))
        except Exception as e:
            logger = LoggerManager().get_logger("EmailWeb")
            logger.error(f"載入收件夾頁面失敗: {e}")
            return f"載入頁面失敗: {e}", 500
    
    @web_bp.route('/email/<int:email_id>')
    def email_detail(email_id: int):
        """郵件詳情頁面"""
        try:
            email = email_service.get_email_detail_api(email_id)
            
            if not email['success']:
                return f"找不到郵件: {email_id}", 404
            
            return render_template('email_detail.html', 
                                 email=email['data'])
        except Exception as e:
            logger = LoggerManager().get_logger("EmailWeb")
            logger.error(f"載入郵件詳情頁面失敗: {e}")
            return f"載入頁面失敗: {e}", 500
    
    return web_bp
"""
郵件處理引擎
整合所有解析器，提供統一的郵件處理服務
支援 Outlook 2019+，採用非同步處理和現代架構模式
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass, field
import uuid
from concurrent.futures import ThreadPoolExecutor

from src.data_models.email_models import (
    EmailData, 
    EmailParsingResult, 
    ProcessingStatus,
    EmailProcessingContext
)
from src.application.interfaces.email_reader import EmailReader
from src.application.interfaces.task_queue import TaskQueue, TaskPriority
from src.infrastructure.adapters.outlook.outlook_adapter import OutlookAdapter, MonitoringConfig
from src.infrastructure.parsers.base_parser import ParserFactory
from src.infrastructure.logging.logger_manager import LoggerManager
from src.infrastructure.adapters.notification.line_notification_service import LineNotificationService


@dataclass
class EmailProcessingConfig:
    """郵件處理配置"""
    max_concurrent_processing: int = 5
    retry_attempts: int = 3
    processing_timeout: float = 300.0
    enable_monitoring: bool = True
    outlook_version: str = "2019"
    batch_size: int = 10
    enable_statistics: bool = True
    temp_directory: str = "/tmp/outlook_processing"
    supported_versions: List[str] = field(default_factory=lambda: ["2016", "2019", "2021", "365"])


@dataclass
class ProcessingMetrics:
    """處理指標"""
    total_processed: int = 0
    total_failed: int = 0
    total_success: int = 0
    processing_start_time: float = field(default_factory=time.time)
    average_processing_time: float = 0.0
    last_processing_time: Optional[float] = None
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_processed == 0:
            return 0.0
        return (self.total_success / self.total_processed) * 100.0
    
    @property
    def uptime(self) -> float:
        """運行時間（秒）"""
        return time.time() - self.processing_start_time


class EmailProcessor:
    """
    郵件處理引擎
    整合解析器、佇列、監控等組件，提供完整的郵件處理服務
    """
    
    def __init__(
        self,
        email_reader: EmailReader,
        task_queue: TaskQueue, 
        outlook_adapter: OutlookAdapter,
        config: EmailProcessingConfig
    ):
        """初始化郵件處理器"""
        self.email_reader = email_reader
        self.task_queue = task_queue
        self.outlook_adapter = outlook_adapter
        self.config = config
        
        # 初始化組件
        self.logger = LoggerManager().get_logger("EmailProcessor")
        self.parser_factory = ParserFactory()
        
        # 初始化LINE通知服務
        try:
            self.line_notification_service = LineNotificationService()
            self.logger.info("LINE通知服務已初始化")
        except Exception as e:
            self.logger.warning(f"LINE通知服務初始化失敗: {e}")
            self.line_notification_service = None
        
        # 狀態管理
        self._is_running = False
        self._active_tasks: Set[asyncio.Task] = set()
        self._processing_lock = asyncio.Lock()
        
        # 指標追蹤
        self.metrics = ProcessingMetrics()
        self._processing_times: List[float] = []
        
        # 線程池（用於CPU密集型任務）
        # 確保 max_workers 至少為 1
        max_workers = max(1, self.config.max_concurrent_processing)
        self._thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        
        self.logger.info(f"郵件處理器已初始化，支援 Outlook {self.config.outlook_version}")
    
    @property
    def is_running(self) -> bool:
        """檢查是否運行中"""
        return self._is_running
    
    @property
    def processed_count(self) -> int:
        """已處理郵件數量"""
        return self.metrics.total_processed
    
    @property 
    def failed_count(self) -> int:
        """失敗郵件數量"""
        return self.metrics.total_failed
    
    async def process_email(self, email_data: EmailData) -> EmailParsingResult:
        """
        處理單一郵件
        
        Args:
            email_data: 郵件數據
            
        Returns:
            處理結果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"開始處理郵件: {email_data.message_id}")
            
            # 驗證郵件數據
            if not self.validate_email_data(email_data):
                raise ValueError("郵件數據驗證失敗")
            
            # 建立處理上下文
            context = EmailProcessingContext(
                email_data=email_data,
                processing_start_time=datetime.now()
            )
            
            # 識別廠商並獲取最佳解析器
            best_parser, vendor_result = self.parser_factory.identify_vendor(email_data)
            
            if not best_parser or not vendor_result.is_identified:
                # 沒有合適的解析器
                result = EmailParsingResult(
                    vendor_code="UNKNOWN",
                    is_success=False,
                    error_message="無法識別廠商或格式"
                )
            else:
                # 使用解析器處理
                from src.infrastructure.parsers.base_parser import ParsingContext, ParsingStrategy
                parsing_context = ParsingContext(
                    email_data=email_data,
                    vendor_code=vendor_result.vendor_code,
                    parsing_strategy=ParsingStrategy.HYBRID
                )
                result = best_parser.parse_email(parsing_context)
            
            # 更新指標
            processing_time = time.time() - start_time
            await self._update_metrics(result.is_success, processing_time)
            
            # 記錄結果
            if result.is_success:
                self.logger.info(
                    f"郵件處理成功: {email_data.message_id}, "
                    f"廠商: {result.vendor_code}, "
                    f"處理時間: {processing_time:.2f}s"
                )
            else:
                self.logger.warning(
                    f"郵件處理失敗: {email_data.message_id}, "
                    f"錯誤: {result.error_message}"
                )
            
            # 發送LINE通知
            if self.line_notification_service:
                try:
                    email_dict = {
                        'id': email_data.message_id,
                        'subject': email_data.subject,
                        'sender': email_data.sender,
                        'received_time': email_data.received_time.isoformat() if email_data.received_time else None,
                        'vendor_code': result.vendor_code,
                        'extraction_method': getattr(result, 'extraction_method', 'N/A'),
                        'error_message': result.error_message,
                        'pd': getattr(result, 'pd', 'N/A'),
                        'lot': getattr(result, 'lot', 'N/A'),
                        'yield_value': getattr(result, 'yield_value', 'N/A')
                    }
                    
                    if result.is_success:
                        self.line_notification_service.send_parsing_success_notification(email_dict)
                    else:
                        self.line_notification_service.send_parsing_failure_notification(email_dict)
                except Exception as e:
                    self.logger.error(f"發送LINE通知失敗: {e}")
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            await self._update_metrics(False, processing_time)
            
            error_msg = f"郵件處理發生異常: {str(e)}"
            self.logger.error(error_msg)
            
            # 異常情況也發送LINE通知
            if self.line_notification_service:
                try:
                    email_dict = {
                        'id': email_data.message_id,
                        'subject': email_data.subject,
                        'sender': email_data.sender,
                        'received_time': email_data.received_time.isoformat() if email_data.received_time else None,
                        'vendor_code': 'ERROR',
                        'extraction_method': 'N/A',
                        'error_message': error_msg,
                        'pd': 'N/A',
                        'lot': 'N/A',
                        'yield_value': 'N/A'
                    }
                    self.line_notification_service.send_parsing_failure_notification(email_dict)
                except Exception as notify_e:
                    self.logger.error(f"發送異常通知失敗: {notify_e}")
            
            return EmailParsingResult(
                vendor_code="ERROR",
                is_success=False,
                error_message=error_msg
            )
    
    async def process_batch(self, emails: List[EmailData]) -> List[EmailParsingResult]:
        """
        批次處理郵件
        
        Args:
            emails: 郵件列表
            
        Returns:
            處理結果列表
        """
        self.logger.info(f"開始批次處理 {len(emails)} 封郵件")
        
        # 使用 asyncio.Semaphore 限制並發數量
        semaphore = asyncio.Semaphore(self.config.max_concurrent_processing)
        
        async def process_with_semaphore(email: EmailData) -> EmailParsingResult:
            async with semaphore:
                return await self.process_email(email)
        
        # 並發處理所有郵件
        tasks = [process_with_semaphore(email) for email in emails]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 處理異常結果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"批次處理郵件 {i} 發生異常: {str(result)}")
                processed_results.append(EmailParsingResult(
                    vendor_code="ERROR",
                    is_success=False,
                    error_message=str(result)
                ))
            else:
                processed_results.append(result)
        
        success_count = sum(1 for r in processed_results if r.is_success)
        self.logger.info(f"批次處理完成: {success_count}/{len(emails)} 成功")
        
        return processed_results
    
    async def process_email_with_retry(self, email_data: EmailData) -> EmailParsingResult:
        """
        帶重試的郵件處理
        
        Args:
            email_data: 郵件數據
            
        Returns:
            處理結果
        """
        last_error = None
        
        for attempt in range(self.config.retry_attempts):
            try:
                result = await self.process_email(email_data)
                
                if result.is_success:
                    if attempt > 0:
                        self.logger.info(f"郵件重試處理成功: {email_data.message_id}, 嘗試次數: {attempt + 1}")
                    return result
                
                last_error = result.error_message
                
                # 如果不是暫時性錯誤，不重試
                if self._is_permanent_error(result.error_message):
                    break
                
            except Exception as e:
                last_error = str(e)
                self.logger.warning(f"郵件處理嘗試 {attempt + 1} 失敗: {str(e)}")
            
            # 等待後重試
            if attempt < self.config.retry_attempts - 1:
                await asyncio.sleep(2 ** attempt)  # 指數退避
        
        self.logger.error(f"郵件重試處理最終失敗: {email_data.message_id}")
        return EmailParsingResult(
            vendor_code="ERROR",
            is_success=False,
            error_message=last_error or "重試次數耗盡"
        )
    
    def _is_permanent_error(self, error_message: str) -> bool:
        """判斷是否為永久性錯誤"""
        permanent_errors = [
            "無法識別廠商",
            "郵件數據驗證失敗",
            "不支援的郵件格式"
        ]
        
        return any(err in (error_message or "") for err in permanent_errors)
    
    def validate_email_data(self, email_data: EmailData) -> bool:
        """
        驗證郵件數據
        
        Args:
            email_data: 郵件數據
            
        Returns:
            驗證是否通過
        """
        try:
            if not email_data.message_id:
                return False
                
            if not email_data.subject and not email_data.body:
                return False
            
            return True
            
        except Exception:
            return False
    
    def is_outlook_version_supported(self) -> bool:
        """檢查 Outlook 版本是否支援"""
        return self.config.outlook_version in self.config.supported_versions
    
    def validate_configuration(self) -> bool:
        """驗證配置"""
        try:
            if self.config.max_concurrent_processing <= 0:
                return False
            
            if self.config.retry_attempts < 0:
                return False
            
            if self.config.processing_timeout <= 0:
                return False
            
            if not self.is_outlook_version_supported():
                return False
            
            return True
            
        except Exception:
            return False
    
    async def connect_to_outlook(self) -> bool:
        """連接到 Outlook"""
        try:
            return await self.outlook_adapter.connect()
        except Exception as e:
            self.logger.error(f"連接 Outlook 失敗: {str(e)}")
            return False
    
    async def fetch_new_emails(self) -> List[EmailData]:
        """獲取新郵件"""
        try:
            return await self.outlook_adapter.get_inbox_emails()
        except Exception as e:
            self.logger.error(f"獲取新郵件失敗: {str(e)}")
            return []
    
    def enqueue_email_processing(self, email_data: EmailData) -> str:
        """將郵件加入處理佇列"""
        try:
            # 檢查是否有運行中的事件循環
            loop = None
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                # 沒有運行中的事件循環，返回空字符串
                self.logger.warning("沒有運行中的事件循環，無法加入佇列")
                return ""
            
            # 如果有事件循環，創建任務
            task = asyncio.create_task(
                self.task_queue.enqueue(email_data, TaskPriority.MEDIUM)
            )
            return str(task)
            
        except Exception as e:
            self.logger.error(f"郵件加入佇列失敗: {str(e)}")
            return ""
    
    def get_processing_status(self, task_id: str) -> str:
        """獲取處理狀態"""
        try:
            task_info = asyncio.create_task(self.task_queue.get_task_info(task_id))
            return task_info.status.value if task_info else "unknown"
        except Exception:
            return "unknown"
    
    async def monitor_inbox_once(self) -> List[EmailData]:
        """執行一次收件夾監控"""
        try:
            return await self.outlook_adapter.get_inbox_emails(unread_only=True)
        except Exception as e:
            self.logger.error(f"收件夾監控失敗: {str(e)}")
            return []
    
    async def process_attachments(self, email_data: EmailData) -> List[str]:
        """處理郵件附件"""
        try:
            return await self.outlook_adapter.download_attachments(email_data)
        except Exception as e:
            self.logger.error(f"處理附件失敗: {str(e)}")
            return []
    
    async def start_continuous_monitoring(self, callback=None) -> None:
        """開始持續監控"""
        try:
            self._is_running = True
            
            monitoring_config = MonitoringConfig(
                check_interval=30.0,
                max_emails_per_check=50,
                enable_real_time=True
            )
            
            async def email_callback(new_emails: List[EmailData]) -> None:
                """新郵件回調"""
                if new_emails:
                    self.logger.info(f"收到 {len(new_emails)} 封新郵件")
                    
                    # 批次處理新郵件
                    results = await self.process_batch(new_emails)
                    
                    # 調用用戶回調
                    if callback:
                        await callback(new_emails, results)
            
            await self.outlook_adapter.start_monitoring(email_callback, monitoring_config)
            
        except Exception as e:
            self.logger.error(f"開始持續監控失敗: {str(e)}")
            self._is_running = False
    
    async def stop_monitoring(self) -> None:
        """停止監控"""
        try:
            self._is_running = False
            await self.outlook_adapter.stop_monitoring()
            self.logger.info("已停止監控")
        except Exception as e:
            self.logger.error(f"停止監控失敗: {str(e)}")
    
    async def graceful_shutdown(self, timeout: float = 30.0) -> None:
        """優雅關閉"""
        try:
            self.logger.info("開始優雅關閉...")
            
            # 停止監控
            await self.stop_monitoring()
            
            # 等待活躍任務完成
            if self._active_tasks:
                self.logger.info(f"等待 {len(self._active_tasks)} 個活躍任務完成...")
                await asyncio.wait_for(
                    asyncio.gather(*self._active_tasks, return_exceptions=True),
                    timeout=timeout
                )
            
            # 斷開 Outlook 連接
            await self.outlook_adapter.disconnect()
            
            # 關閉線程池
            self._thread_pool.shutdown(wait=True)
            
            self.logger.info("優雅關閉完成")
            
        except asyncio.TimeoutError:
            self.logger.warning("優雅關閉超時，強制關閉")
        except Exception as e:
            self.logger.error(f"優雅關閉發生錯誤: {str(e)}")
    
    def get_processing_metrics(self) -> Dict[str, Any]:
        """獲取處理指標"""
        return {
            "processed_count": self.metrics.total_processed,
            "failed_count": self.metrics.total_failed,
            "success_count": self.metrics.total_success,
            "success_rate": self.metrics.success_rate,
            "uptime": self.metrics.uptime,
            "average_processing_time": self.metrics.average_processing_time,
            "last_processing_time": self.metrics.last_processing_time,
            "active_tasks": len(self._active_tasks),
            "is_running": self._is_running
        }
    
    async def _update_metrics(self, success: bool, processing_time: float) -> None:
        """更新處理指標"""
        async with self._processing_lock:
            self.metrics.total_processed += 1
            
            if success:
                self.metrics.total_success += 1
            else:
                self.metrics.total_failed += 1
            
            # 更新處理時間統計
            self._processing_times.append(processing_time)
            if len(self._processing_times) > 100:  # 保留最近100次
                self._processing_times.pop(0)
            
            self.metrics.average_processing_time = sum(self._processing_times) / len(self._processing_times)
            self.metrics.last_processing_time = processing_time
    
    def log_processing_event(self, event: str, context: Dict[str, Any]) -> None:
        """記錄處理事件"""
        self.logger.info(f"處理事件: {event}", extra={"context": context})